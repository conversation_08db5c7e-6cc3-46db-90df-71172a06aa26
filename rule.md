## Vue 3 + Vite + Element Plus 开发规范 (JavaScript版)

### 1\. 核心理念

  * **一致性**：所有团队成员遵循同一套标准，降低沟通和维护成本。
  * **可读性**：代码首先是写给人看的，其次才是给机器执行的。清晰易懂的代码至关重要。
  * **组件化**：拥抱 Vue 的核心思想，构建高内聚、低耦合、可复用的组件。
  * **拥抱 `<script setup>`**：全面使用 Vue 3 的组合式 API (`Composition API`) 与 `<script setup>` 语法糖，它更简洁、更高效。

### 2\. 项目结构

一个良好、可扩展的目录结构是项目成功的一半。

```bash
.
├── public/                  # 静态资源，不会被 Vite 处理，直接复制到 dist 目录
├── src/
│   ├── api/                 # API 请求模块 (例如: user.js, product.js)
│   ├── assets/              # 项目中使用的静态资源 (图片、字体等，会被 Vite 处理)
│   ├── components/          # 全局通用组件
│   │   ├── common/          # 基础通用组件 (如: SvgIcon, BaseCard)
│   │   └── layout/          # 布局组件 (如: Header, Sidebar, Footer)
│   ├── router/              # 路由配置 (index.js)
│   ├── store/               # 状态管理 (Pinia)
│   │   └── modules/         # Pinia 模块 (如: user.js)
│   ├── styles/              # 全局样式
│   │   ├── index.css        # 全局样式入口
│   │   └── variables.css    # 全局 CSS 变量
│   ├── utils/               # 工具函数 (如: request.js, format.js)
│   ├── views/               # 页面级组件 (一个路由对应一个文件)
│   │   ├── user/
│   │   │   └── Profile.vue
│   │   └── Login.vue
│   ├── App.vue              # 根组件
│   └── main.js              # 应用入口
├── .editorconfig            # 编辑器配置
├── .env.development         # 开发环境变量
├── .env.production          # 生产环境变量
├── .eslintrc.cjs            # ESLint 配置文件
├── .gitignore               # Git 忽略配置
├── .prettierrc.json         # Prettier 配置文件
├── index.html               # 入口 HTML
├── package.json
└── vite.config.js           # Vite 配置文件
```

### 3\. 命名规范

  * **组件文件**：使用 **大驼峰命名法 (PascalCase)**，且应为多个单词。例如：`MyButton.vue`, `UserAvatar.vue`。

      * **推荐**：`UserProfile.vue`
      * **禁止**：`user-profile.vue`, `Profile.vue` (单个单词可能与 HTML 元素冲突)

  * **JS文件**：使用 **小驼峰命名法 (camelCase)** 或 **中划线命名法 (kebab-case)**。推荐统一使用 **kebab-case** 以便和路由、URL风格统一。例如：`use-user-store.js`, `api-client.js`。

  * **变量与函数**：使用 **小驼峰命名法 (camelCase)**。

      * `const buttonText = 'Submit';`
      * `function getUserInfo() { ... }`

  * **常量**：使用 **全大写和下划线 (UPPER\_CASE\_SNAKE\_CASE)**。

      * `const MAX_LOGIN_ATTEMPTS = 5;`

  * **Props**：在 `<script>` 中使用 **camelCase**，在 `<template>` 中使用 **kebab-case**。

    ```vue
    <script setup>
    defineProps({
      userName: String
    });
    </script>

    <MyComponent user-name="Alice" />
    ```

  * **Events**：自定义事件名使用 **kebab-case**，在 `<script>` 中通过 `defineEmits` 定义时使用 **camelCase**。

    ```vue
    <script setup>
    const emit = defineEmits(['update:modelValue']);
    emit('update:modelValue', 'new value');
    </script>

    <Child @update:model-value="handleUpdate" />
    ```

### 4\. Vue 组件开发规范

#### 4.1. 组件结构顺序

推荐使用以下顺序，保持一致性：

```vue
<script setup>
// 1. 导入 (Imports)
import { ref, computed } from 'vue';
import UserAvatar from '@/components/common/UserAvatar.vue';

// 2. Props & Emits 定义
const props = defineProps({ /* ... */ });
const emit = defineEmits(['submit']);

// 3. 响应式状态 (Reactive State)
const count = ref(0);

// 4. 计算属性 (Computed Properties)
const doubleCount = computed(() => count.value * 2);

// 5. 方法 (Methods)
function increment() {
  count.value++;
  emit('submit', count.value);
}

// 6. 生命周期钩子 (Lifecycle Hooks)
// onMounted, etc.
</script>

<template>
  </template>

<style scoped>
/* 样式 */
</style>
```

#### 4.2. `<script setup>` 最佳实践

  * **强制使用** `<script setup>`，这是 Vue 3 的最佳实践。
  * **Props 定义**：必须明确类型，推荐提供 `default` 值。对于非必需的 prop，`required: false` 是默认值，可以不写。
    ```javascript
    defineProps({
      title: {
        type: String,
        required: true
      },
      items: {
        type: Array,
        default: () => [] // 对象或数组的默认值必须用工厂函数返回
      },
      visible: {
        type: Boolean,
        default: false
      }
    });
    ```
  * **Emits 定义**：明确列出所有会触发的事件。
    ```javascript
    const emit = defineEmits(['change', 'delete']);
    ```
  * **v-for 指令**：必须提供 `key`，且 `key` 必须是唯一的、稳定的值（如 `item.id`），不要使用 `index` 作为 `key`。
    ```html
    <div v-for="item in items" :key="item.id">...</div>

    <div v-for="(item, index) in items" :key="index">...</div>
    ```

#### 4.3. 样式 (Style)

  * **强制使用 `<style scoped>`**，以避免组件间的样式污染。
  * 如果需要修改子组件或 Element Plus 组件的深层样式，使用 `:deep()` 选择器。
    ```css
    /* App.vue */
    <style scoped>
    .container :deep(.el-button) {
      margin-right: 10px;
    }
    </style>
    ```

### 5\. Element Plus 使用规范

#### 5.1. 按需自动导入 (推荐)

为了生产环境的性能，**必须**使用按需导入，而不是全局完整引入。最佳实践是使用 `unplugin-vue-components` 和 `unplugin-auto-import` 插件，它们会自动帮你完成。

**安装依赖:**

```bash
npm install -D unplugin-vue-components unplugin-auto-import
```

**配置 `vite.config.js`:**

```javascript
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
});
```

这样配置后，你就可以在模板中直接使用 Element Plus 组件，无需手动 `import`。

#### 5.2. 图标使用

推荐使用 Element Plus 官方的图标库 `@element-plus/icons-vue`。同样可以通过 `unplugin-vue-components` 实现自动导入。

#### 5.3. 主题定制

通过创建自己的 SCSS 样式文件来覆盖 Element Plus 的默认变量，以实现主题定制。不要直接在组件的 `scoped` 样式中硬编码修改。

### 6\. 环境变量

  * 在项目根目录创建 `.env.development` 和 `.env.production` 文件。
  * 所有环境变量必须以 `VITE_` 开头。
    ```ini
    # .env.production
    VITE_APP_TITLE = 'My Awesome App'
    VITE_API_BASE_URL = 'https://api.prod.com/v1'
    ```
  * 在代码中通过 `import.meta.env.VITE_XXX` 访问。
