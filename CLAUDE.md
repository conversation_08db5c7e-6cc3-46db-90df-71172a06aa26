# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Vue 3 + Element Plus + Pinia的ERP管理系统前端项目，使用现代化的前端技术栈：
- Vue 3 (Composition API + `<script setup>`)
- Element Plus UI框架
- Pinia状态管理
- Vue Router路由管理
- Vite构建工具
- Tailwind CSS样式框架
- Axios HTTP客户端

## 开发命令

### 常用命令
```bash
# 安装依赖
yarn install

# 开发服务器
yarn dev

# 生产构建
yarn build

# 预览构建结果
yarn preview

# 代码格式化
yarn format

# ESLint检查并修复
yarn lint

# 仅ESLint检查
yarn lint:check
```

## 核心架构

### 状态管理架构
- **Pinia Store**: 使用组合式API风格，位于`src/store/modules/`
- **Layout Store**: `src/store/modules/layout.js` - 控制布局配置、侧边栏折叠、主题设置等
- **User Store**: `src/store/modules/user.js` - 用户认证和信息管理
- **Permission Store**: `src/store/modules/permission.js` - 权限管理、动态菜单和按钮权限
- **Tabs Store**: `src/store/modules/tabs.js` - 标签页管理
- **统一导出**: `src/store/index.js` - 统一导出所有store模块

### 路由架构
- **路由配置**: `src/router/index.js` - 包含路由守卫和权限控制
- **布局路由**: 使用嵌套路由，主布局为`AppLayout.vue`
- **认证守卫**: 检查token进行路由权限控制
- **动态路由**: `src/utils/dynamic-router.js` - 根据用户权限动态生成路由
- **动态标题**: 根据路由meta自动设置页面标题

### 布局系统
- **灵活布局**: 支持侧边栏模式和顶部模式两种布局
- **Layout Store**: 管理布局配置、主题、侧边栏状态
- **响应式设计**: 使用CSS变量和固定尺寸保证布局稳定性
- **主题系统**: 支持浅色/深色主题切换

### HTTP请求架构
- **Axios封装**: `src/utils/request.js` - 统一的HTTP客户端
- **请求拦截**: 自动添加Authorization头
- **响应拦截**: 统一错误处理和token过期处理
- **API模块**: `src/api/` - 按业务模块组织API调用

## 技术特性

### Vue 3 特性
- **Composition API**: 全面使用`<script setup>`语法
- **自动导入**: 使用`unplugin-auto-import`和`unplugin-vue-components`
- **TypeScript支持**: 生成类型定义文件`auto-imports.d.ts`和`components.d.ts`

### Element Plus集成
- **按需导入**: 使用unplugin插件自动按需导入组件
- **主题定制**: 通过CSS变量自定义主题色彩
- **图标系统**: 使用`@element-plus/icons-vue`图标库

### 开发工具配置
- **ESLint**: 使用扁平化配置，支持Vue 3和现代JS语法
- **Vite**: 现代构建工具，包含Vue插件和开发工具
- **路径别名**: `@`指向`src`目录

## 开发规范

### 组件开发
- **强制使用**`<script setup>`语法
- **Props定义**: 必须明确类型和默认值
- **样式隔离**: 使用`<style scoped>`，深层样式使用`:deep()`
- **命名规范**: 组件文件使用PascalCase，JS文件使用kebab-case

### 代码风格
- **变量命名**: 使用camelCase
- **常量命名**: 使用UPPER_CASE_SNAKE_CASE
- **Props**: script中camelCase，template中kebab-case
- **Events**: 使用kebab-case

### 状态管理
- **Pinia Store**: 使用组合式API风格
- **模块化**: 按业务功能分离store模块
- **持久化**: 布局配置自动保存到localStorage

## 特别注意事项

### 布局系统
- 布局模式通过`LAYOUT_MODES`常量管理
- 侧边栏宽度固定：展开200px，收缩60px
- 头部高度固定：48px
- 使用CSS变量确保布局一致性

### 菜单系统
- 使用Element Plus原生菜单组件
- 支持多级菜单展开收缩
- 侧边栏和顶部模式统一交互逻辑

### 主题系统
- 支持预设主题和自定义主题
- 主题配置可导出/导入
- 深色模式通过CSS变量实现

### 权限控制
- 基于token的身份验证
- 路由级别的权限控制
- 动态菜单权限：根据用户权限显示菜单
- 按钮级权限：通过`v-permission`指令控制
- 自动处理token过期和重定向

## 项目结构要点

- `src/components/layout/`: 布局组件，支持多种布局模式
- `src/constants/`: 常量定义，如布局模式、主题配置等
- `src/composables/`: 组合式函数，如`useDarkMode`
- `src/directives/`: 自定义指令，如权限控制指令
- `src/views/`: 页面组件，按业务模块组织
- `src/utils/`: 工具函数，如认证、请求、动态路由等
- `issues/`: 记录已解决的技术问题和解决方案

## 业务模块架构

### 系统管理模块
- **用户管理**: `src/views/system/user/` - 用户CRUD、权限分配
- **角色管理**: `src/views/system/role/` - 角色CRUD、权限配置
- **菜单管理**: `src/views/system/menu/` - 菜单树形结构管理
- **权限管理**: `src/views/system/permission/` - 权限配置页面
- **操作日志**: `src/views/system/operationsLogs/` - 系统操作记录

### 组件复用模式
- **页面组件**: 在`views/*/index.vue`中作为路由入口
- **业务组件**: 在`views/*/components/`中按功能拆分
- **表单组件**: 如`UserForm.vue`、`RoleForm.vue`等封装表单逻辑
- **表格组件**: 如`UserTable.vue`等封装列表展示逻辑
- **搜索组件**: 如`UserSearch.vue`等封装查询条件