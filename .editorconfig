# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript/TypeScript/Vue files
[*.{js,jsx,mjs,cjs,ts,tsx,vue}]
indent_size = 2
max_line_length = 100

# CSS/SCSS/Less files
[*.{css,scss,less,styl}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# Package files
[{package.json,*.lock}]
indent_size = 2

# HTML files
[*.html]
indent_size = 2 