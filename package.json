{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/", "lint": "eslint src --ext .vue,.js,.jsx,.ts,.tsx --fix", "lint:check": "eslint src --ext .vue,.js,.jsx,.ts,.tsx"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "element-plus": "^2.9.9", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.18", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.3.0", "postcss": "^8.4.34", "prettier": "3.5.3", "sass-embedded": "^1.89.2", "tailwindcss": "^3.4.1", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}