# 二次分拣设置项优化任务

## 任务背景
用户希望实现一个弹出框配置项，其中包含勾选项和下拉选择，会保存到后端，并且设置的值会被携带到 getList 方法的参数中。原有实现存在数据绑定和参数构建的问题。

## 问题分析
1. **数据绑定问题**：直接修改 props 违反 Vue 最佳实践
2. **参数传递逻辑复杂**：getList 方法中条件判断过于冗长
3. **代码结构不规范**：缺少响应式数据的本地副本
4. **默认值设置混乱**：在多个地方设置默认值，容易产生冲突
5. **错误处理不完善**：API 调用缺少适当的错误处理

## 优化方案

### 1. SettingDialog 组件优化
- ✅ 创建 settingData 的本地响应式副本 (`localSettingData`)
- ✅ 使用 `reactive` 和 `watch` 管理表单数据
- ✅ 修复直接修改 props 的问题
- ✅ 优化数据同步逻辑

### 2. SecondSortIndex 主组件优化
- ✅ 使用 `computed` 属性处理扫描参数构建 (`scanParams`)
- ✅ 简化 getList 方法逻辑
- ✅ 统一默认值设置
- ✅ 增强错误处理和用户提示
- ✅ 取消注释 API 调用代码

### 3. 用户体验改进
- ✅ 添加设置保存成功提示
- ✅ 优化加载状态和错误信息展示
- ✅ 增加输入验证和重复扫描检查

## 技术实现细节

### 响应式数据管理
```javascript
// 本地数据副本
const localSettingData = reactive({
  seedWallType: '',
  // ... 其他字段
})

// 监听 props 变化
watch(() => props.settingData, (newData) => {
  if (newData) {
    Object.assign(localSettingData, newData)
  }
}, { immediate: true, deep: true })
```

### 参数构建优化
```javascript
// 使用计算属性简化参数构建
const scanParams = computed(() => {
  const params = { noBuildSKuInfo: true, whCode: 'CA' }
  
  // 播种墙设置
  if (settingData.value.seedWallType) {
    params.seedWallType = settingData.value.seedWallType
    // ...
  }
  
  return params
})
```

### 错误处理增强
```javascript
try {
  const res = await secondSortScan(params)
  if (res.code === 200 && res.data) {
    ElMessage.success('扫描成功')
    // ...
  } else {
    ElMessage.error(res.message || '获取数据失败')
  }
} catch (error) {
  ElMessage.error('网络错误，请重试')
}
```

## 符合开发规范

1. **命名规范**：使用 camelCase 命名变量和函数
2. **组件结构**：按照推荐顺序组织代码（导入、Props、响应式状态、计算属性、方法、生命周期）
3. **Vue 3 最佳实践**：使用 `<script setup>` 和组合式 API
4. **Element Plus 集成**：正确使用组件和消息提示

## 测试验证
- [ ] 设置弹窗打开和关闭功能
- [ ] 配置项保存到后端
- [ ] 扫描时正确携带设置参数
- [ ] 错误处理和用户提示
- [ ] 默认值加载和显示

## 完成状态
✅ 代码重构完成
⏳ 等待测试验证 