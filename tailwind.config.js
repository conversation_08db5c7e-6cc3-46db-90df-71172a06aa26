/** @type {import('tailwindcss').Config} */
export default {
    content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
    theme: {
        extend: {
            colors: {
                primary: {
                    DEFAULT: '#409EFF',
                    light: '#53a8ff',
                    dark: '#337ecc',
                },
            },
            boxShadow: {
                'card': '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
            }
        },
    },
    plugins: [],
    corePlugins: {
        preflight: false, // 避免与Element Plus的样式冲突
    },
} 