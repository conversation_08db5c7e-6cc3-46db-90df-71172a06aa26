import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, logout, getUserInfo as fetchUserInfo } from '@/api/user'
import { setToken, getToken, clearAuth, setUserInfo, getUserInfo as getStoredUserInfo } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
    // 状态
    const token = ref(getToken())
    const userInfo = ref(getStoredUserInfo() || {})
    const permissions = ref([]) // 添加权限数组
    const roles = ref([]) // 添加角色数组
    const isLoggedIn = ref(!!getToken())
    const isLoading = ref(false)
    const isLoadingUserInfo = ref(false)

    // 计算属性 - 显示名称
    const displayName = computed(() => {
        return userInfo.value?.name || userInfo.value?.username || userInfo.value?.email || '管理员'
    })

    // 登录
    async function loginAction(loginData) {
        try {
            isLoading.value = true
            const response = await login(loginData)

            // 从API响应中获取token
            const newToken = response.data.token

            // 保存token
            token.value = newToken
            setToken(newToken)

            // 更新登录状态
            isLoggedIn.value = true

            // 获取用户信息
            await getUserInfo()

            // 登录成功提示
            ElMessage.success('登录成功')

            return true
        } catch {
            ElMessage.error('登录失败，请重试')
            return false
        } finally {
            isLoading.value = false
        }
    }

    // 登出
    async function logoutAction() {
        try {
            isLoading.value = true

            // 调用登出接口
            await logout()

            // 清除权限数据和动态路由
            const { usePermissionStore } = await import('./permission')
            const permissionStore = usePermissionStore()
            permissionStore.clearPermissionData()

            // 清除标签页数据
            const { useTabsStore } = await import('./tabs')
            const tabsStore = useTabsStore()
            tabsStore.clearAllTabs()

            // 清除本地存储的认证信息
            clearAuth()

            // 重置状态
            token.value = null
            userInfo.value = {}
            permissions.value = []
            roles.value = []
            isLoggedIn.value = false

            // 登出成功提示
            ElMessage.success('已安全登出')

            return true
        } catch {
            ElMessage.error('登出失败，请重试')
            return false
        } finally {
            isLoading.value = false
        }
    }

    // 获取用户信息
    async function getUserInfo(force = false) {
        // 如果已有用户信息且不强制刷新，则直接返回
        if (!force && userInfo.value && userInfo.value.id) {
            return userInfo.value
        }

        try {
            if (!token.value) {
                return null
            }

            // 避免重复请求
            if (isLoadingUserInfo.value) {
                return userInfo.value
            }

            isLoadingUserInfo.value = true
            const response = await fetchUserInfo()
            const data = response.data

            // 更新用户信息和权限数据
            userInfo.value = data.user || data
            permissions.value = data.permissions || []
            roles.value = data.roles || []
            
            setUserInfo(data.user || data)

            console.log('用户信息加载成功:', {
                user: userInfo.value,
                permissions: permissions.value,
                roles: roles.value
            })

            return data
        } catch {
            // 静默处理用户信息获取失败，不显示错误提示
            return null
        } finally {
            isLoadingUserInfo.value = false
        }
    }

    // 验证token是否有效
    async function validateToken() {
        if (!token.value) {
            return false
        }

        try {
            const userInfoResult = await getUserInfo(true)
            return !!userInfoResult
        } catch {
            return false
        }
    }

    return {
        token,
        userInfo,
        permissions,
        roles,
        isLoggedIn,
        isLoading,
        isLoadingUserInfo,
        displayName,
        loginAction,
        logoutAction,
        getUserInfo,
        validateToken
    }
}) 