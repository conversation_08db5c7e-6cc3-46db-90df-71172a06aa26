import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getUserMenus } from '@/api/menu'
import { addDynamicRoutes, removeDynamicRoutes } from '@/utils/dynamic-router'
import { useUserStore } from './user'

/**
 * 权限管理Store
 * 负责管理用户菜单权限、按钮权限和动态路由
 */
export const usePermissionStore = defineStore('permission', () => {
  const menus = ref([])
  const rawMenus = ref([])
  const buttons = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  const hasMenus = computed(() => menus.value.length > 0)
  
  const hasPermission = computed(() => (permission) => {
    if (!permission) return true
    return checkMenuPermission(rawMenus.value, permission)
  })

  const hasAllPermissions = computed(() => (permissions) => {
    if (!permissions || permissions.length === 0) return true

    if (!Array.isArray(permissions)) {
      permissions = [permissions]
    }

    return permissions.every(p => checkMenuPermission(rawMenus.value, p))
  })

  function checkMenuPermission(menus, permission) {
    if (!menus || menus.length === 0) return false
    if (!permission) return true

    if (Array.isArray(permission)) {
      return permission.some(p => checkSingleMenuPermission(menus, p))
    }

    return checkSingleMenuPermission(menus, permission)
  }

  function checkSingleMenuPermission(menus, permission) {
    for (const menu of menus) {
      if (menu.permission === permission) {
        return true
      }

      if (menu.children && menu.children.length > 0) {
        if (checkSingleMenuPermission(menu.children, permission)) {
          return true
        }
      }
    }
    return false
  }

  /**
   * 获取用户菜单和权限数据
   * @returns {Promise<boolean>} 是否获取成功
   */
  async function fetchUserMenus() {
    if (isLoading.value) return false

    isLoading.value = true
    error.value = null

    try {
      // 确保先获取用户信息和权限
      const userStore = useUserStore()
      await userStore.getUserInfo()

      const response = await getUserMenus()
      
      if (response.code === 200 && response.data) {
        const rawMenuData = response.data.menus || []
        const buttonPermissions = response.data.buttons || []

        // 存储原始数据
        rawMenus.value = rawMenuData
        buttons.value = buttonPermissions

        // 添加动态路由
        const routeSuccess = addDynamicRoutes(rawMenuData)
        if (!routeSuccess) {
          console.warn('动态路由添加失败，但权限数据已加载')
        }

        // 处理菜单数据用于UI显示
        menus.value = processMenusForUI(rawMenuData)

        return true
      } else {
        throw new Error(response.message || '获取用户菜单失败')
      }
    } catch (err) {
      error.value = err
      console.error('获取用户菜单失败:', err)
      
      // 失败时清理数据
      clearPermissionData()
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 处理菜单数据用于UI显示
   * @param {Array} menuData - 原始菜单数据
   * @returns {Array} 处理后的菜单数据
   */
  function processMenusForUI(menuData) {
    if (!Array.isArray(menuData)) return []

    return menuData
      .filter(menu => menu.type === 0 && menu.status === 1) // 只保留启用的菜单
      .map(menu => {
        // 确保路径格式正确（保持完整路径用于菜单跳转）
        let menuPath = menu.path
        if (!menuPath.startsWith('/')) {
          menuPath = `/${menuPath}`
        }

        return {
          id: menu.id,
          name: menu.name,
          path: menuPath,
          icon: menu.icon,
          permission: menu.permission,
          hidden: menu.hidden === 1,
          sort: menu.sort,
          children: menu.children ? processMenusForUI(menu.children) : []
        }
      })
      .sort((a, b) => (a.sort || 0) - (b.sort || 0)) // 按排序字段排序
  }

  /**
   * 清理权限数据和动态路由
   */
  function clearPermissionData() {
    // 清理动态路由
    removeDynamicRoutes()
    
    // 清理状态数据
    menus.value = []
    rawMenus.value = []
    buttons.value = []
    error.value = null
  }

  /**
   * 重新加载权限数据
   * @returns {Promise<boolean>} 是否重新加载成功
   */
  async function reloadPermissions() {
    clearPermissionData()
    return await fetchUserMenus()
  }

  /**
   * 检查菜单是否应该显示
   * @param {Object} menu - 菜单项
   * @returns {boolean} 是否显示
   */
  function shouldShowMenu(menu) {
    if (!menu) return false
    
    // 检查是否隐藏
    if (menu.hidden) return false
    
    // 检查权限
    if (menu.permission && !hasPermission.value(menu.permission)) {
      return false
    }
    
    return true
  }

  /**
   * 获取可见的菜单列表
   * @returns {Array} 可见菜单数组
   */
  const visibleMenus = computed(() => {
    return filterVisibleMenus(menus.value)
  })

  /**
   * 递归过滤可见菜单
   * @param {Array} menuList - 菜单列表
   * @returns {Array} 过滤后的菜单列表
   */
  function filterVisibleMenus(menuList) {
    return menuList
      .filter(menu => shouldShowMenu(menu))
      .map(menu => ({
        ...menu,
        children: menu.children ? filterVisibleMenus(menu.children) : []
      }))
  }

  return {
    // 状态
    menus,
    rawMenus,
    buttons,
    isLoading,
    error,
    
    // 计算属性
    hasMenus,
    hasPermission,
    hasAllPermissions,
    visibleMenus,
    
    // 方法
    fetchUserMenus,
    clearPermissionData,
    reloadPermissions,
    shouldShowMenu
  }
})
