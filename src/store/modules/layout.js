import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { DEFAULT_LAYOUT_CONFIG, LAYOUT_MODES } from '@/constants/layout'

export const useLayoutStore = defineStore('layout', () => {
  // 状态
  const layoutConfig = ref(getStoredConfig())
  const isSettingsVisible = ref(false)
  const isCollapsed = ref(false)

  // 计算属性
  const currentLayoutMode = computed(() => layoutConfig.value.layoutMode)
  const sidebarConfig = computed(() => layoutConfig.value.sidebarConfig)
  const headerConfig = computed(() => layoutConfig.value.headerConfig)
  const themeConfig = computed(() => layoutConfig.value.themeConfig)

  // 是否显示侧边栏
  const showSidebar = computed(() => {
    return currentLayoutMode.value === LAYOUT_MODES.SIDEBAR
  })

  // 是否显示顶部菜单
  const showTopMenu = computed(() => {
    return currentLayoutMode.value === LAYOUT_MODES.TOP
  })

  // 侧边栏实际宽度（使用固定值）
  const sidebarWidth = computed(() => {
    if (!showSidebar.value) return 0
    return isCollapsed.value ? 60 : 200 // 固定值：展开200px，收起60px
  })

  // 方法

  /**
   * 从localStorage获取配置
   */
  function getStoredConfig() {
    try {
      const stored = localStorage.getItem('layout-config')
      if (stored) {
        const config = JSON.parse(stored)
        // 合并默认配置，确保新增配置项不丢失
        return mergeConfig(DEFAULT_LAYOUT_CONFIG, config)
      }
    } catch (_err) {
      // 静默处理，不显示错误提示
    }
    return { ...DEFAULT_LAYOUT_CONFIG }
  }

  /**
   * 深度合并配置对象
   */
  function mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig }

    for (const key in userConfig) {
      if (userConfig[key] && typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
        merged[key] = { ...defaultConfig[key], ...userConfig[key] }
      } else {
        merged[key] = userConfig[key]
      }
    }

    return merged
  }

  /**
   * 保存配置到localStorage
   */
  function saveConfig() {
    try {
      localStorage.setItem('layout-config', JSON.stringify(layoutConfig.value))
    } catch (_err) {
      // 静默处理，不显示错误提示
    }
  }



  /**
   * 更新布局模式
   */
  function updateLayoutMode(mode) {
    layoutConfig.value.layoutMode = mode
    saveConfig()
  }

  /**
   * 更新侧边栏配置
   */
  function updateSidebarConfig(config) {
    layoutConfig.value.sidebarConfig = { ...layoutConfig.value.sidebarConfig, ...config }
    saveConfig()
    applyCSSVariables()
  }



  /**
   * 更新主题配置
   */
  function updateThemeConfig(config) {
    layoutConfig.value.themeConfig = { ...layoutConfig.value.themeConfig, ...config }
    saveConfig()
    applyCSSVariables()
  }



  /**
   * 切换侧边栏折叠状态
   */
  function toggleSidebar() {
    isCollapsed.value = !isCollapsed.value
  }

  /**
   * 设置侧边栏折叠状态
   */
  function setSidebarCollapsed(collapsed) {
    isCollapsed.value = collapsed
  }

  /**
   * 显示/隐藏设置面板
   */
  function toggleSettings() {
    isSettingsVisible.value = !isSettingsVisible.value
  }

  /**
   * 设置面板显示状态
   */
  function setSettingsVisible(visible) {
    isSettingsVisible.value = visible
  }

  /**
   * 重置为默认配置
   */
  function resetToDefault() {
    layoutConfig.value = { ...DEFAULT_LAYOUT_CONFIG }
    isCollapsed.value = false
    saveConfig()
    applyCSSVariables()
  }

  /**
   * 应用CSS变量（简化版）
   */
  function applyCSSVariables() {
    const root = document.documentElement

    // 主题颜色
    root.style.setProperty('--el-color-primary', themeConfig.value.primaryColor)

    // 侧边栏（使用固定值）
    root.style.setProperty('--layout-sidebar-width', '200px')
    root.style.setProperty('--layout-sidebar-collapsed-width', '60px')

    // 头部高度（使用固定值）
    root.style.setProperty('--layout-header-height', '48px')

    // 设置默认的过渡动画
    root.style.setProperty('--layout-transition-duration', '0.3s')
  }

  // 监听配置变化，自动应用CSS变量
  watch(
    () => layoutConfig.value,
    () => {
      applyCSSVariables()
    },
    { deep: true, immediate: true }
  )

  return {
    // 状态
    layoutConfig,
    isSettingsVisible,
    isCollapsed,

    // 计算属性
    currentLayoutMode,
    sidebarConfig,
    headerConfig,
    themeConfig,
    showSidebar,
    showTopMenu,
    sidebarWidth,

    // 方法
    updateLayoutMode,
    updateSidebarConfig,
    updateThemeConfig,
    toggleSidebar,
    setSidebarCollapsed,
    toggleSettings,
    setSettingsVisible,
    resetToDefault,
    applyCSSVariables
  }
}) 