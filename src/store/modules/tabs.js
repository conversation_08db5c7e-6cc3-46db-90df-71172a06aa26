import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useTabsStore = defineStore('tabs', () => {
  // 状态
  const tabs = ref(getStoredTabs())
  const activeTab = ref(getStoredActiveTab())

  // 计算属性
  const currentTab = computed(() => {
    return tabs.value.find(tab => tab.name === activeTab.value)
  })

  // 方法

  /**
   * 从 localStorage 获取存储的标签页
   */
  function getStoredTabs() {
    try {
      const stored = localStorage.getItem('app-tabs')
      if (stored) {
        const parsedTabs = JSON.parse(stored)
        // 确保至少有仪表盘标签页
        if (parsedTabs.length === 0) {
          return getDefaultTabs()
        }
        // 确保每个标签页都有必要的字段
        return parsedTabs.map(tab => ({
          ...tab,
          query: tab.query || {},
          params: tab.params || {},
          fullPath: tab.fullPath || tab.path
        }))
      }
    } catch (_err) {
      // 静默处理，不显示错误提示
    }
    return getDefaultTabs()
  }

  /**
   * 从 localStorage 获取存储的活动标签页
   */
  function getStoredActiveTab() {
    try {
      const stored = localStorage.getItem('app-active-tab')
      if (stored) {
        return stored
      }
    } catch (error) {
      // 静默处理，不显示错误提示
    }
    return 'dashboard'
  }

  /**
   * 获取默认标签页
   */
  function getDefaultTabs() {
    return [
      {
        name: 'dashboard',
        title: '仪表盘',
        path: '/dashboard',
        query: {},
        params: {},
        fullPath: '/dashboard',
        closable: false // 首页不可关闭
      }
    ]
  }



  /**
   * 保存标签页到 localStorage
   */
  function saveTabs() {
    try {
      localStorage.setItem('app-tabs', JSON.stringify(tabs.value))
      localStorage.setItem('app-active-tab', activeTab.value)
    } catch (_err) {
      // 静默处理，不显示错误提示
    }
  }

  /**
   * 添加标签页
   */
  function addTab(route) {
    const { name, path, meta, query, params } = route
    let tabName = name || path.replace('/', '')

    // 特殊处理：将 Dashboard 和 dashboard 路径都映射到 dashboard 标签页
    if (name === 'Dashboard' || path === '/dashboard' || tabName === 'Dashboard') {
      tabName = 'dashboard'
    }

    // 检查标签页是否已存在（基于路径，不包含参数）
    const existingTab = tabs.value.find(tab => tab.name === tabName)
    if (existingTab) {
      // 更新现有标签页的路由信息
      existingTab.path = path
      existingTab.query = query || {}
      existingTab.params = params || {}
      existingTab.fullPath = route.fullPath
      activeTab.value = tabName
      saveTabs()
      return
    }

    // 添加新标签页
    const newTab = {
      name: tabName,
      title: meta?.title || name || '未命名',
      path: path,
      query: query || {},
      params: params || {},
      fullPath: route.fullPath,
      closable: tabName !== 'dashboard' // 仪表盘标签页不可关闭
    }

    tabs.value.push(newTab)
    activeTab.value = tabName
    saveTabs()
  }

  /**
   * 移除标签页
   */
  function removeTab(tabName) {
    const index = tabs.value.findIndex(tab => tab.name === tabName)
    if (index === -1) return

    const tab = tabs.value[index]
    if (!tab.closable) return // 不可关闭的标签页

    tabs.value.splice(index, 1)

    // 如果关闭的是当前活动标签页，需要切换到其他标签页
    if (activeTab.value === tabName) {
      if (tabs.value.length > 0) {
        // 优先选择右侧标签页，如果没有则选择左侧
        const nextTab = tabs.value[index] || tabs.value[index - 1]
        activeTab.value = nextTab.name
        saveTabs()
        return nextTab // 返回下一个标签页，让组件处理路由导航
      }
    }
    saveTabs()
    return null
  }

  /**
   * 切换标签页
   */
  function switchTab(tabName) {
    const tab = tabs.value.find(tab => tab.name === tabName)
    if (tab) {
      activeTab.value = tabName
      saveTabs()
      // 返回完整的路由信息，包含查询参数
      return {
        path: tab.path,
        query: tab.query || {},
        params: tab.params || {},
        fullPath: tab.fullPath
      }
    }
    return null
  }

  /**
   * 关闭其他标签页
   */
  function closeOtherTabs(tabName) {
    const targetTab = tabs.value.find(tab => tab.name === tabName)
    if (!targetTab) return

    tabs.value = tabs.value.filter(tab => !tab.closable || tab.name === tabName)
    activeTab.value = tabName
    saveTabs()
  }

  /**
   * 关闭所有标签页
   */
  function closeAllTabs() {
    tabs.value = tabs.value.filter(tab => !tab.closable)
    if (tabs.value.length > 0) {
      activeTab.value = tabs.value[0].name
      saveTabs()
      return tabs.value[0] // 返回第一个标签页，让组件处理路由导航
    }
    saveTabs()
    return null
  }

  /**
   * 根据路由更新当前活动标签页
   */
  function updateActiveTabByRoute(route) {
    const { name, path } = route
    let tabName = name || path.replace('/', '')

    // 特殊处理：将 Dashboard 和 dashboard 路径都映射到 dashboard 标签页
    if (name === 'Dashboard' || path === '/dashboard' || tabName === 'Dashboard') {
      tabName = 'dashboard'
    }

    const existingTab = tabs.value.find(tab => tab.name === tabName)
    if (existingTab) {
      activeTab.value = tabName
      saveTabs()
    }
  }

  /**
   * 清除所有标签页数据（用于登出时重置）
   */
  function clearAllTabs() {
    // 重置为默认标签页
    tabs.value = getDefaultTabs()
    activeTab.value = 'dashboard'

    // 清除 localStorage 中的数据
    try {
      localStorage.removeItem('app-tabs')
      localStorage.removeItem('app-active-tab')
    } catch (_err) {
      // 静默处理，不显示错误提示
    }
  }

  return {
    // 状态
    tabs,
    activeTab,

    // 计算属性
    currentTab,

    // 方法
    addTab,
    removeTab,
    switchTab,
    closeOtherTabs,
    closeAllTabs,
    updateActiveTabByRoute,
    clearAllTabs
  }
})
