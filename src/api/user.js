import request from '@/utils/request'

/**
 * 用户登录
 * @param {Object} data - 登录参数 {username, password}
 * @returns {Promise} 登录请求的Promise
 */
export function login(data) {
    return request({
        url: '/login',
        method: 'post',
        data
    })
}

/**
 * 用户登出
 * @returns {Promise} 登出请求的Promise
 */
export function logout() {
    return request({
        url: '/logout',
        method: 'post'
    })
}

/**
 * 获取用户信息
 * @returns {Promise} 获取用户信息的Promise
 */
export function getUserInfo() {
    return request({
        url: '/userinfo',
        method: 'get'
    })
}

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 获取用户列表的Promise
 */
export function getUserList(params) {
    return request({
        url: '/users',
        method: 'get',
        params
    })
}

export function getAllUserList(params) {
    return request({
        url: '/users/all',
        method: 'get',
        params
    })
}

/**
 * 获取用户信息
 */
export function getUserListInfo(id) {
    return request({
        url: '/users/' + id,
        method: 'get'
    })
}

/**
 * 添加用户
 * @param {Object} data - 需要添加的用户数据
 * @returns {Promise} 添加用户的Promise
 */
export function addUser(data) {
    return request({
        url: '/users',
        method: 'post',
        data: {
            name: data.name,
            email: data.email,
            password: data.password,
            confirm_password: data.confirmPassword,
            roles: data.roles || []
        }
    })
}

/**
 * 更新用户
 * @param {Object} data - 需要更新的用户数据
 * @returns {Promise} 更新用户的Promise
 */
export function updateUser(data) {
    return request({
        url: '/users/edit/' + data.id,
        method: 'post',
        data: {
            name: data.name,
            email: data.email,
            roles: data.roles || []
        }
    })
}

/**
 * 删除用户
 * @param {Object} data - 需要删除的用户数据
 * @returns {Promise} 删除用户的Promise
 */
export function deleteUser(data) {
    return request({
        url: '/users/delete/' + data.id,
        method: 'post',
        data
    })
}

/**
 * 批量删除用户
 * @param {Object} data - 需要批量删除的用户数据
 * @returns {Promise} 批量删除用户的Promise
 */
export function batchDeleteUser(data) {
    return request({
        url: '/users/batch-delete',
        method: 'post',
        data
    })
}

/**
 * 重置密码
 * @param {Object} data - 需要重置密码的用户数据，包含id, password和confirm_password
 * @returns {Promise} 重置密码的Promise
 */
export function resetPassword(data) {
    return request({
        url: `/users/reset/${data.id}`,
        method: 'post',
        data: {
            password: data.password,
            confirm_password: data.confirm_password
        }
    })
}

/**
 * 更新用户信息
 * @param {Object} data - 需要更新的用户信息
 * @returns {Promise} 更新用户信息的Promise
 */
export function updateUserInfo(data) {
    return request({
        url: '/user/profile',
        method: 'post',
        data
    })
}