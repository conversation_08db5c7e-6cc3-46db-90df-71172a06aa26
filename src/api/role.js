import request from '@/utils/request';

/**
 * 获取角色列表
 * @returns {Promise<object>}
 */
export function getRoleList() {
    return request({
        url: '/roles',
        method: 'get'
    });
}

/**
 * 新增角色
 * @param {Object} data
 * @returns {Promise<object>}
 */
export function addRole(data) {
    return request({
        url: '/roles',
        method: 'post',
        data
    });
}

/**
 * 编辑角色
 * @param {Object} data
 * @returns {Promise<object>}
 */
export function editRole(data) {
    return request({
        url: '/roles/edit/' + data.id,
        method: 'post',
        data
    });
}

/**
 * 修改角色状态
 * @param {number} id
 * @returns {Promise<object>}
 */
export function updateRoleStatus(id) {
    return request({
        url: '/roles/change-status/' + id,
        method: 'post',
    });
}

/**
 * 删除角色
 * @param {number} id
 * @returns {Promise<object>}
 */
export function deleteRole(id) {
    return request({
        url: '/roles/delete/' + id,
        method: 'post'
    });
}


/**
 * 分配权限给角色
 * @param {number} id
 * @param {Object} data
 * @returns {Promise<object>}
 */
export function assignPermissions(id, data) {
    return request({
        url: '/roles/' + id + '/permissions',
        method: 'post',
        data
    });
}