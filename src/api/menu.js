import request from '@/utils/request';

/**
 * 获取当前登录用户的菜单和按钮权限
 * @returns {Promise<object>}
 */
export function getUserMenus() {
    return request({
        url: '/menus/user-menus',
        method: 'get'
    });
}

/**
 * 获取菜单树
 * @returns {Promise<object>}
 */
export function getMenuTree() {
    return request({
        url: '/menus/tree',
        method: 'get'
    });
}

/**
 * 新增菜单
 * @param {Object} data
 * @returns {Promise<object>}
 */
export function addMenu(data) {
    return request({
        url: '/menus',
        method: 'post',
        data
    });
}


/**
 * 编辑菜单
 * @param {Object} data
 * @returns {Promise<object>}
 */
export function editMenu(data) {
    return request({
        url: '/menus/edit/' + data.id,
        method: 'post',
        data
    });
}


/**
 * 删除菜单
 * @param {number} id
 * @returns {Promise<object>}
 */
export function deleteMenu(id) {
    return request({
        url: '/menus/delete/' + id,
        method: 'post'
    });
}

/**
 * 修改菜单状态
 * @param {number} id
 * @returns {Promise<object>}
 */
export function changeStatus(id) {
    return request({
        url: '/menus/change-status/' + id,
        method: 'get',
    });
}