import request from '@/utils/request'

/**
 * 截单列表
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function listing(data) {
  return request({
    url: '/outbound/intercept/listing',
    method: 'post',
    data,
  })
}

/**
 * 查询参数
 * @returns {Promise} options
 */
export function options() {
  return request({
    url: '/outbound/intercept/options',
    method: 'post',
  })
}

/**
 * 分组状态数量统计
 * @returns {Promise} list
 */
export function statusSum(data) {
  return request({
    url: '/outbound/intercept/statusSum',
    method: 'post',
    data,
  })
}

/**
 * 客户列表
 * @returns {Promise} consumerList
 */
export function consumers(data) {
  return request({
    url: '/outbound/consumers',
    method: 'post',
    data,
  })
}

/**
 * 渠道列表
 * @returns {Promise} channelList
 */
export function channels(data) {
  return request({
    url: '/outbound/channels',
    method: 'post',
    data,
  })
}

/**
 * 截单详情
 * @returns {Promise} list
 */
export function detail(data) {
  return request({
    url: '/outbound/intercept/detail',
    method: 'post',
    data,
  })
}

/**
 * 截单详情 - 日志
 * @returns {Promise} list
 */
export function detailLogs(data) {
  return request({
    url: '/outbound/intercept/logs',
    method: 'post',
    data,
  })
}

/**
 * 截单详情 - 面单/附件预览下载地址
 * @returns {Promise} list
 */
export function appendixPreview(data) {
  return request({
    url: '/outbound/intercept/appendixPreview',
    method: 'post',
    data,
  })
}
