import request from '@/utils/request'

/**
 * 复核验货 - queryIntercepting
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function queryIntercepting(data) {
  return request({
    url: '/outbound/review/query',
    method: 'post',
    data,
  })
}

/**
 * 复核验货 - 扫描单号
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function reviewScan(data) {
  return request({
    url: '/outbound/review/scan',
    method: 'post',
    data,
  })
}

/**
 * 复核验货 - 扫描单号 (一单多件)
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function reviewScanMore(data) {
  return request({
    url: '/outbound/review/scanMore',
    method: 'post',
    data,
  })
}

/**
 * 复核验货 - 获取用户打印设置
 */
export function getPrinter() {
  return request({
    url: '/outbound/review/getPrinter',
    method: 'post',
  })
}

/**
 * 复核验货 - 用户打印设置
 * @param {Object} data - 打印设置参数
 */
export function setPrinter(data) {
  return request({
    url: '/outbound/review/setPrinter',
    method: 'post',
    data,
  })
}

