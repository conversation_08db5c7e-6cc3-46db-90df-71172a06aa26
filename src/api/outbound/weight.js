import request from '@/utils/request'

/**
 * 称重 - 扫描单号
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function weightScan(data) {
  return request({
    url: '/outbound/weight/scan',
    method: 'post',
    data,
  })
}

/**
 * 称重 - 获取用户打印设置
 */
export function getPrinter() {
  return request({
    url: '/outbound/weight/getPrinter',
    method: 'post',
  })
}

/**
 * 称重 - 用户打印设置
 * @param {Object} data - 打印设置参数
 */
export function setPrinter(data) {
  return request({
    url: '/outbound/weight/setPrinter',
    method: 'post',
    data,
  })
}

/**
 * 称重 - 完成称重
 * @param {Object} data - 称重参数
 */
export function toWeight(data) {
  return request({
    url: '/outbound/weight/finish',
    method: 'post',
    data,
  })
}
