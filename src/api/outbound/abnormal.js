import request from '@/utils/request'

/**
 * 获取异常件列表
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function getAbnormalList(data) {
  return request({
    url: '/gateway/wms/exception/page',
    method: 'post',
    data,
  })
}

/**
 * 批量处理异常件
 * @param {Object} data - 处理参数
 * @returns {Promise}
 */
export function batchProcessAbnormal(data) {
  // 暂时使用模拟数据，等待后端API
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('批量处理数据:', data)
      const mockData = {
        code: 200,
        message: '批量处理成功',
        data: null
      }
      resolve(mockData)
    }, 500)
  })
}

/**
 * 导出异常件数据
 * @param {Object} data - 导出参数
 * @returns {Promise}
 */
export function exportAbnormalData(data) {
  // 暂时使用模拟数据，等待后端API
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('导出数据:', data)
      const mockData = {
        code: 200,
        message: '导出成功',
        data: null
      }
      resolve(mockData)
    }, 1000)
  })
}

/**
 * 获取异常件详情
 * @param {string} id - 异常件ID
 * @returns {Promise}
 */
export function getAbnormalDetail(id) {
  // 暂时使用模拟数据，等待后端API
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        code: 200,
        message: 'success',
        data: {
          id: id,
          barcode: 'BC123456789',
          platformOrderNo: 'PO123456789',
          waveNo: 'W123456789',
          sourceNo: 'SO123456789',
          expressNo: 'EX123456789',
          customerCode: 'CUST001',
          customerName: '测试客户',
          logisticsChannel: 'FE-W',
          logisticsCarrier: '顺丰',
          outboundType: '1',
          exceptionType: '地址异常',
          exceptionReason: '收货地址不详',
          exceptionTime: '2025-07-11 10:30:00',
          createTime: '2025-07-11 09:00:00',
          status: '待处理',
          remark: '客户联系中'
        }
      }
      resolve(mockData)
    }, 300)
  })
}
