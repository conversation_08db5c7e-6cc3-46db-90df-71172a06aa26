import request from '@/utils/request';

/**
 * 获取操作日志列表
 * @returns {Promise<object>}
 */
export function getOperationLogList(params) {
    return request({
        url: 'operation-logs',
        method: 'get',
        params
    });
}

/**
 * 删除操作日志
 * @param {number} id
 * @returns {Promise<object>}
 */
export function deleteOperationLog(id) {
    return request({
        url: 'operation-logs/delete/' + id,
        method: 'post'
    });
}

/**
 * 批量删除操作日志
 * @param {number[]} ids
 * @returns {Promise<object>}
 */
export function deleteOperationLogBatch(data) {
    return request({
        url: 'operation-logs/batch-delete',
        method: 'post',
        data
    });
}