<script setup>
import { ref, markRaw } from 'vue'
import { Histogram, TrendCharts, DataLine, Connection, User, ShoppingCart, UserFilled, Message } from '@element-plus/icons-vue'

// 统计数据
const stats = ref([
  { title: '用户数量', count: '1,286', icon: markRaw(User), color: '#0071e3', bg: '#f0f7ff' },
  { title: '订单总数', count: '4,592', icon: markRaw(ShoppingCart), color: '#34c759', bg: '#f0faf5' },
  { title: '活跃用户', count: '782', icon: markRaw(UserFilled), color: '#ff9500', bg: '#fff8f0' },
  { title: '系统消息', count: '24', icon: markRaw(Message), color: '#ff2d55', bg: '#fff2f5' }
])

// 系统信息
const systemInfo = ref([
  { label: '系统版本', value: 'v1.0.0' },
  { label: '服务器环境', value: 'Ubuntu 22.04' },
  { label: '数据库', value: 'MySQL 8.0' },
  { label: '缓存服务', value: 'Redis 6.2' }
])
</script>

<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <div class="stat-cards">
      <el-card 
        v-for="(item, index) in stats" 
        :key="index"
        class="stat-card"
        shadow="hover"
      >
        <div class="stat-content" :style="{ borderColor: item.color }">
          <div class="stat-info">
            <div class="stat-title">{{ item.title }}</div>
            <div class="stat-count" :style="{ color: item.color }">{{ item.count }}</div>
          </div>
          <div class="stat-icon" :style="{ backgroundColor: item.bg, color: item.color }">
            <el-icon><component :is="item.icon" /></el-icon>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 主要内容区 -->
    <div class="main-content">
      <!-- 欢迎卡片 -->
      <el-card class="welcome-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>欢迎使用后台管理系统</span>
          </div>
        </template>
        <div class="welcome-content">
          <div class="welcome-text">
            <h3>您好，管理员！</h3>
            <p>欢迎使用Vue3 + Element Plus后台管理系统模板。</p>
            <p>本系统使用了最新的前端技术栈，包括Vue3、Vite、Pinia和Element Plus等。</p>
            <p>如有任何问题，请随时联系技术支持团队。</p>
          </div>
          <div class="welcome-image">
            <img src="https://element-plus.org/images/element-plus-logo.svg" alt="Logo" />
          </div>
        </div>
      </el-card>
      
      <!-- 系统信息卡片 -->
      <el-card class="system-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>系统信息</span>
          </div>
        </template>
        <div class="system-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item 
              v-for="(item, index) in systemInfo" 
              :key="index"
              :label="item.label"
            >
              {{ item.value }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
    
    <!-- 快捷功能区 -->
    <div class="quick-access">
      <el-card class="access-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>快捷功能</span>
          </div>
        </template>
        <div class="access-buttons">
          <el-button type="primary" :icon="markRaw(Histogram)" class="function-button">数据统计</el-button>
          <el-button type="success" :icon="markRaw(TrendCharts)" class="function-button">系统监控</el-button>
          <el-button type="warning" :icon="markRaw(DataLine)" class="function-button">数据报表</el-button>
          <el-button type="info" :icon="markRaw(Connection)" class="function-button">系统配置</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 0;
}

/* 统计卡片 */
.stat-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: var(--el-box-shadow-light);
  border: none;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  height: 100%;
  border-left: 4px solid transparent;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin-bottom: 10px;
}

.stat-count {
  font-size: 28px;
  font-weight: 500;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  font-size: 24px;
}

/* 主要内容区 */
.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.welcome-card, .system-card, .access-card {
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  height: 100%;
  box-shadow: var(--el-box-shadow-light) !important;
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  padding: 16px;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
}

.welcome-text {
  flex: 1;
}

.welcome-text h3 {
  font-size: 20px;
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.welcome-text p {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin: 10px 0;
}

.welcome-image {
  margin-left: 20px;
  flex-shrink: 0;
}

.welcome-image img {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

/* 系统信息卡片 */
.system-info {
  padding: 20px;
}

/* 快捷功能区 */
.quick-access {
  margin-bottom: 20px;
}

.access-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 20px;
}

.function-button {
  flex: 1;
  min-width: 140px;
  border-radius: 8px;
  height: 44px;
  font-size: 15px;
  transition: all 0.3s;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.function-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}


</style> 