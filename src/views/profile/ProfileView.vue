<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useUserStore } from '@/store'
import { ElMessage } from 'element-plus'
import { User, Lock, Message, Calendar, Edit, Check } from '@element-plus/icons-vue'
import { updateUserInfo } from '@/api/user'

// 用户Store
const userStore = useUserStore()

// 是否编辑模式
const isEditing = ref(false)

// 加载状态
const loading = ref(false)
const saveLoading = ref(false)

// 用户信息表单
const userForm = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 原始用户信息，用于重置
const originalUserInfo = reactive({})

// 密码是否可见
const showPassword = ref(false)

// 表单校验规则
const rules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度应在2到20个字符之间', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { min: 6, max: 20, message: '密码长度应在6到20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { 
      validator: (rule, value, callback) => {
        if (userForm.password && value !== userForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
})

// 表单引用
const formRef = ref(null)

// 初始化表单数据
const initFormData = () => {
  const userInfo = userStore.userInfo
  
  // 复制用户信息到表单
  userForm.name = userInfo.name || ''
  userForm.email = userInfo.email || ''
  userForm.password = ''
  userForm.confirmPassword = ''
  
  // 保存原始信息用于重置
  Object.assign(originalUserInfo, {
    name: userInfo.name || '',
    email: userInfo.email || ''
  })
}

// 切换编辑模式
const toggleEditMode = () => {
  if (isEditing.value) {
    // 退出编辑模式，重置表单
    isEditing.value = false
    resetForm()
  } else {
    // 进入编辑模式
    isEditing.value = true
  }
}

// 重置表单
const resetForm = () => {
  userForm.name = originalUserInfo.name
  userForm.email = originalUserInfo.email
  userForm.password = ''
  userForm.confirmPassword = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 保存用户信息
const saveUserInfo = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saveLoading.value = true
    
    // 构建更新数据
    const updateData = {
      name: userForm.name,
      email: userForm.email
    }
    
    // 如果有填写密码，则添加密码
    if (userForm.password) {
      updateData.password = userForm.password
    }
    
    try {
      // 调用API更新用户信息
      await updateUserInfo(updateData)
      
      // 更新Store中的用户信息
      userStore.userInfo = {
        ...userStore.userInfo,
        ...updateData
      }
      
      // 更新本地存储的用户信息
      localStorage.setItem('admin_user_info', JSON.stringify(userStore.userInfo))
      
      // 更新原始信息
      Object.assign(originalUserInfo, {
        name: userForm.name,
        email: userForm.email
      })
      
      // 清空密码字段
      userForm.password = ''
      userForm.confirmPassword = ''
      
      // 保存成功提示
      ElMessage.success('个人信息已更新')
      
      // 退出编辑模式
      isEditing.value = false
    } catch {
      ElMessage.error('更新用户信息失败')
    } finally {
      saveLoading.value = false
    }
  } catch {
    // 表单验证失败，用户界面已显示错误信息
  }
}

// 获取用户加入时间的格式化显示
const joinTime = computed(() => {
  const createdAt = userStore.userInfo?.created_at
  if (!createdAt) return '未知'
  
  // 简单的日期格式化
  const date = new Date(createdAt)
  return date.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
})

// 页面加载时初始化
onMounted(async () => {
  loading.value = true
  
  try {
    // 确保有最新的用户信息
    await userStore.getUserInfo()
    
    // 初始化表单数据
    initFormData()
  } catch {
    ElMessage.error('加载用户信息失败')
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="profile-container">
    <el-card class="profile-card" shadow="hover" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span class="header-title">个人信息</span>
          <el-button 
            :icon="isEditing ? Check : Edit" 
            :type="isEditing ? 'primary' : 'default'" 
            @click="isEditing ? saveUserInfo() : toggleEditMode()"
            :loading="saveLoading"
            size="small"
          >
            {{ isEditing ? '保存' : '编辑' }}
          </el-button>
        </div>
      </template>
      
      <div class="profile-content">
        <!-- 用户头像和基本信息 -->
        <div class="user-header">
          <el-avatar :size="100" class="user-avatar">{{ userForm.name?.slice(0, 1) || userStore.userInfo?.email?.slice(0, 1) || '用' }}</el-avatar>
          <div class="user-info">
            <h2 class="user-name">{{ userStore.userInfo?.name || userStore.userInfo?.email || '未设置姓名' }}</h2>
            <p class="join-time">
              <el-icon><Calendar /></el-icon>
              <span>加入时间：{{ joinTime }}</span>
            </p>
          </div>
        </div>
        
        <!-- 用户信息表单 -->
        <el-form 
          ref="formRef"
          :model="userForm"
          :rules="rules"
          label-position="top"
          class="user-form"
          :disabled="!isEditing"
        >
          <el-form-item prop="name" label="姓名">
            <el-input
              v-model="userForm.name"
              placeholder="请输入姓名"
              clearable
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="email" label="邮箱">
            <el-input
              v-model="userForm.email"
              placeholder="请输入邮箱"
              clearable
              :disabled="true"
            >
              <template #prefix>
                <el-icon><Message /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <template v-if="isEditing">
            <el-divider content-position="left">修改密码（可选）</el-divider>
            
            <el-form-item prop="password" label="新密码">
              <el-input
                v-model="userForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="留空则不修改密码"
                show-password
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item prop="confirmPassword" label="确认新密码">
              <el-input
                v-model="userForm.confirmPassword"
                :type="showPassword ? 'text' : 'password'"
                placeholder="再次输入新密码"
                show-password
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-form>
        
        <!-- 编辑模式下的按钮 -->
        <div v-if="isEditing" class="form-actions">
          <el-button @click="toggleEditMode">取消</el-button>
          <el-button type="primary" @click="saveUserInfo" :loading="saveLoading">保存</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.profile-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.profile-card {
  width: 100%;
  max-width: 700px;
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  box-shadow: var(--el-box-shadow-light) !important;
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.profile-content {
  padding: 20px;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.user-avatar {
  font-size: 36px;
  background-color: var(--el-color-primary);
  margin-right: 20px;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 22px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.join-time {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.join-time .el-icon {
  margin-right: 5px;
}

.user-form {
  margin-top: 20px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

:deep(.el-form-item) {
  margin-bottom: 25px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-divider__text) {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

@media (max-width: 768px) {
  .user-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style> 