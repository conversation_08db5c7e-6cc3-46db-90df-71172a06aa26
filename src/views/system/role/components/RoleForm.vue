<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { addRole, editRole } from '@/api/role'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    roleData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:visible', 'success', 'cancel'])

// 角色表单
const roleForm = reactive({
    id: undefined,
    name: '',         // 角色标识 (API中的name)
    role_name: '',    // 角色名称 (API中的role_name)
    guard_name: 'web', // 角色类型，默认为web
    sort: 0,          // 排序 (API中的sort)
    status: 1,        // 状态 (API中的status)
    remark: ''        // 备注 (API中的remark)
})

// 状态选项
const statusOptions = [
    { value: 1, label: '正常' },
    { value: 0, label: '停用' }
]

// 表单校验规则
const roleFormRules = {
    name: [
        { required: true, message: '角色标识不能为空', trigger: 'blur' }
    ],
    role_name: [
        { required: true, message: '角色名称不能为空', trigger: 'blur' }
    ],
    sort: [
        { required: true, message: '角色排序不能为空', trigger: 'blur' }
    ]
}

// 表单引用
const roleFormRef = ref(null)
// 表单提交状态
const loading = ref(false)

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false)
    emit('cancel')
}

// 提交表单
const submitForm = async () => {
    if (!roleFormRef.value) return

    try {
        await roleFormRef.value.validate()
        loading.value = true

        // 确保表单提交的状态是数字类型
        if (roleForm.status !== undefined) {
            roleForm.status = roleForm.status === 0 ? 0 : 1
        }

        // 记录当前操作的角色ID，用于刷新后保持选中
        let operatedRoleId = roleForm.id

        if (roleForm.id) {
            // 编辑角色
            await editRole(roleForm)
            ElMessage.success('修改成功')
        } else {
            // 新增角色
            const res = await addRole(roleForm)
            ElMessage.success('新增成功')
            // 如果是新增，保存返回的ID用于后续选中
            if (res.data && res.data.id) {
                operatedRoleId = res.data.id
            }
        }

        // 关闭对话框
        emit('update:visible', false)

        // 通知父组件操作成功
        emit('success', operatedRoleId)
    } catch (error) {
        console.error('表单提交失败', error)
        ElMessage.error('操作失败，请重试')
    } finally {
        loading.value = false
    }
}

// 重置表单
const resetForm = () => {
    roleForm.id = undefined
    roleForm.name = ''
    roleForm.role_name = ''
    roleForm.guard_name = 'web'
    roleForm.sort = 0
    roleForm.status = 1
    roleForm.remark = ''

    if (roleFormRef.value) {
        roleFormRef.value.resetFields()
    }
}

// 监听prop变化，填充表单数据
const initForm = () => {
    resetForm()

    // 编辑模式下填充表单数据
    if (props.roleData && props.roleData.id) {
        console.log('角色数据:', props.roleData)
        console.log('角色状态类型:', typeof props.roleData.status, '值:', props.roleData.status)

        roleForm.id = props.roleData.id
        roleForm.name = props.roleData.name || ''
        roleForm.role_name = props.roleData.role_name || ''
        roleForm.guard_name = props.roleData.guard_name || 'web'
        roleForm.sort = props.roleData.sort || 0

        // 确保 status 是数字类型 (0 或 1)
        if (props.roleData.status !== undefined) {
            if (typeof props.roleData.status === 'string') {
                roleForm.status = parseInt(props.roleData.status)
            } else if (typeof props.roleData.status === 'number') {
                roleForm.status = props.roleData.status
            } else {
                roleForm.status = 1 // 默认值
            }
            // 确保值只能是 0 或 1
            roleForm.status = roleForm.status === 0 ? 0 : 1
        } else {
            roleForm.status = 1 // 默认值
        }

        roleForm.remark = props.roleData.remark || ''

        console.log('表单状态类型:', typeof roleForm.status, '值:', roleForm.status)
    }
}

// 侦听visible属性变化
watch(() => props.visible, (newVal) => {
    if (newVal) {
        // 当对话框打开时，初始化表单数据
        initForm()
    }
}, { immediate: true })

defineExpose({
    initForm
})
</script>

<template>
    <el-dialog :title="title" :model-value="visible" @update:model-value="emit('update:visible', $event)" width="500px"
        append-to-body destroy-on-close :close-on-click-modal="false" @closed="resetForm">
        <el-form ref="roleFormRef" :model="roleForm" :rules="roleFormRules" label-width="100px">
            <el-form-item label="角色名称" prop="role_name">
                <el-input v-model="roleForm.role_name" placeholder="请输入角色名称" />
            </el-form-item>
            <el-form-item label="权限标识" prop="name">
                <el-input v-model="roleForm.name" placeholder="请输入权限标识" />
            </el-form-item>
            <el-form-item label="角色排序" prop="sort">
                <el-input-number v-model="roleForm.sort" :min="0" :max="999" controls-position="right" />
            </el-form-item>
            <el-form-item label="角色状态" prop="status">
                <el-radio-group v-model="roleForm.status">
                    <el-radio v-for="dict in statusOptions" :key="dict.value" :value="dict.value">
                        {{ dict.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input v-model="roleForm.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="submitForm" :loading="loading">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>