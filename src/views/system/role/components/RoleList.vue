<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { deleteRole } from '@/api/role'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    roleList: {
        type: Array,
        default: () => []
    },
    currentRole: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['select', 'delete', 'add', 'edit'])

// 选择角色
const handleSelectRole = (row) => {
    emit('select', row)
}

// 新增角色
const handleAdd = () => {
    emit('add')
}

// 编辑角色
const handleEdit = (row) => {
    emit('edit', row)
}

// 删除角色
const handleDelete = (row) => {
    ElMessageBox.confirm(`确认删除选中的角色?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        emit('delete', row)
    }).catch(() => {
        ElMessage.info('已取消删除')
    })
}
</script>

<template>
    <el-card class="role-card" shadow="hover">
        <template #header>
            <div class="card-header">
                <span>角色列表</span>
                <el-button type="primary" :icon="Plus" circle size="small" @click="handleAdd" />
            </div>
        </template>

        <div class="role-list">
            <el-table v-loading="loading" :data="roleList" highlight-current-row size="large"
                :max-height="'calc(100vh - 200px)'" @row-click="handleSelectRole"
                :row-class-name="row => currentRole && currentRole.id === row.id ? 'row-active' : ''">
                <el-table-column label="角色名称" prop="role_name" min-width="120" />
                <el-table-column label="角色标识" prop="name" min-width="100" />
                <el-table-column label="状态" width="80" align="center">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                            {{ scope.row.status === 1 ? '正常' : '停用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" link :icon="Edit" @click.stop="handleEdit(scope.row)" />
                        <el-button type="danger" link :icon="Delete" @click.stop="handleDelete(scope.row)" />
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </el-card>
</template>

<style scoped>
.role-card {
    border-radius: 8px;
    height: calc(100vh - 120px);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: none;
}

.role-list {
    height: calc(100% - 56px);
    overflow-y: auto;
}

/* 确保角色列表高度设置正确 */
.role-list :deep(.el-loading-mask) {
    height: 100%;
}

.role-list :deep(.el-table) {
    height: 100%;
}

.role-list :deep(.el-table__inner-wrapper) {
    height: 100%;
}

/* 优化左侧角色列表滚动条样式，与右侧保持一致 */
.role-list::-webkit-scrollbar {
    width: 6px;
}

.role-list::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
}

.role-list::-webkit-scrollbar-track {
    background-color: #f5f7fa;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 500;
}

/* 高亮当前选中行 */
.row-active {
    background-color: var(--el-color-primary-light-9) !important;
}
</style>