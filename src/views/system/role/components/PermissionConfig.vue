<script setup>
import { ref, reactive, watch, computed, defineProps, defineEmits, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { assignPermissions, updateRoleStatus } from '@/api/role'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  currentRole: {
    type: Object,
    default: null,
  },
  menuData: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['status-change', 'loading-change'])

// 权限数据状态
const permissionData = reactive({
  menuIds: [],
  allMenuChecked: false,
  isIndeterminate: false,
  moduleCheckedMap: {},
  menuCheckedMap: {},
  submenuCheckedMap: {},
  actionCheckedMap: {},
})

// 权限树节点类
class PermissionNode {
  constructor(id, type, label, permission = null, parentId = null) {
    this.id = id
    this.type = type // 'module', 'menu', 'submenu', 'action'
    this.label = label
    this.permission = permission
    this.parentId = parentId
    this.children = []
    this.checked = false
    this.indeterminate = false
  }

  addChild(child) {
    child.parentId = this.id
    this.children.push(child)
  }

  // 获取所有后代节点
  getAllDescendants() {
    const descendants = []
    const traverse = (node) => {
      node.children.forEach((child) => {
        descendants.push(child)
        traverse(child)
      })
    }
    traverse(this)
    return descendants
  }

  // 获取所有祖先节点ID路径 - 修复循环引用风险
  getAncestorPath() {
    const path = []
    const visited = new Set()
    let current = this

    while (current.parentId && !visited.has(current.parentId)) {
      visited.add(current.id)
      path.unshift(current.parentId)
      current = findNodeById(current.parentId)
      if (!current) break
    }
    return path
  }

  // 清理节点引用 - 防止内存泄漏
  destroy() {
    this.children.forEach((child) => child.destroy())
    this.children = []
    this.parentId = null
  }
}

// 构建权限树 - 添加错误边界处理
const buildPermissionTree = () => {
  try {
    // 验证输入数据
    if (!props.menuData || !Array.isArray(props.menuData)) {
      console.warn('menuData 不是有效的数组，使用空数据')
      return { tree: [], nodeMap: new Map() }
    }

    const tree = []
    const nodeMap = new Map() // 用于快速查找节点

    props.menuData.forEach((module) => {
      // 验证模块数据结构
      if (!module || typeof module.id === 'undefined') {
        console.warn('发现无效的模块数据，跳过:', module)
        return
      }

      // 创建模块节点
      const moduleNode = new PermissionNode(
        module.id,
        'module',
        module.label || '未命名模块',
        module.permission,
      )
      tree.push(moduleNode)
      nodeMap.set(module.id, moduleNode)

      // 处理子菜单
      if (module.children && Array.isArray(module.children)) {
        module.children.forEach((menu) => {
          if (!menu || typeof menu.id === 'undefined') {
            console.warn('发现无效的菜单数据，跳过:', menu)
            return
          }

          const menuNode = new PermissionNode(
            menu.id,
            'menu',
            menu.label || '未命名菜单',
            menu.permission,
            module.id,
          )
          moduleNode.addChild(menuNode)
          nodeMap.set(menu.id, menuNode)

          // 处理菜单的操作按钮或子菜单
          if (menu.actions && Array.isArray(menu.actions)) {
            // 二级菜单 - 直接包含操作按钮
            menu.actions.forEach((action) => {
              if (!action || typeof action.id === 'undefined') {
                console.warn('发现无效的操作数据，跳过:', action)
                return
              }

              const actionNode = new PermissionNode(
                action.id,
                'action',
                action.label || '未命名操作',
                action.permission,
                menu.id,
              )
              menuNode.addChild(actionNode)
              nodeMap.set(action.id, actionNode)
            })
          } else if (menu.children && Array.isArray(menu.children)) {
            // 三级菜单结构
            menu.children.forEach((submenu) => {
              if (!submenu || typeof submenu.id === 'undefined') {
                console.warn('发现无效的子菜单数据，跳过:', submenu)
                return
              }

              const submenuNode = new PermissionNode(
                submenu.id,
                'submenu',
                submenu.label || '未命名子菜单',
                submenu.permission,
                menu.id,
              )
              menuNode.addChild(submenuNode)
              nodeMap.set(submenu.id, submenuNode)

              // 处理子菜单的操作按钮
              if (submenu.actions && Array.isArray(submenu.actions)) {
                submenu.actions.forEach((action) => {
                  if (!action || typeof action.id === 'undefined') {
                    console.warn('发现无效的子菜单操作数据，跳过:', action)
                    return
                  }

                  const actionNode = new PermissionNode(
                    action.id,
                    'action',
                    action.label || '未命名操作',
                    action.permission,
                    submenu.id,
                  )
                  submenuNode.addChild(actionNode)
                  nodeMap.set(action.id, actionNode)
                })
              }
            })
          }
        })
      }
    })

    return { tree, nodeMap }
  } catch (error) {
    console.error('构建权限树失败:', error)
    return { tree: [], nodeMap: new Map() }
  }
}

// 清理权限树 - 防止内存泄漏
const clearPermissionTree = () => {
  try {
    if (permissionTree.value?.tree) {
      permissionTree.value.tree.forEach((node) => {
        if (node && typeof node.destroy === 'function') {
          node.destroy()
        }
      })
    }
    if (permissionTree.value?.nodeMap) {
      permissionTree.value.nodeMap.clear()
    }
  } catch (error) {
    console.error('清理权限树失败:', error)
  }
}

// 权限树和节点映射
const permissionTree = computed(() => buildPermissionTree())

// 根据ID查找节点 - 添加错误处理
const findNodeById = (id) => {
  try {
    return permissionTree.value?.nodeMap?.get(id) || null
  } catch (error) {
    console.error('查找节点失败:', error)
    return null
  }
}

// 重置权限数据
const resetPermissionData = () => {
  try {
    permissionData.menuIds = []
    permissionData.allMenuChecked = false
    permissionData.isIndeterminate = false
    permissionData.moduleCheckedMap = {}
    permissionData.menuCheckedMap = {}
    permissionData.submenuCheckedMap = {}
    permissionData.actionCheckedMap = {}

    // 重置树节点状态
    if (permissionTree.value?.tree) {
      permissionTree.value.tree.forEach((node) => {
        resetNodeRecursive(node)
      })
    }
  } catch (error) {
    console.error('重置权限数据失败:', error)
  }
}

// 递归重置节点状态
const resetNodeRecursive = (node) => {
  if (!node) return

  try {
    node.checked = false
    node.indeterminate = false
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((child) => resetNodeRecursive(child))
    }
  } catch (error) {
    console.error('重置节点状态失败:', error)
  }
}

// 核心：递归设置节点及其所有子节点的状态
const setNodeAndChildrenRecursive = (node, checked) => {
  if (!node) return

  try {
    node.checked = checked
    node.indeterminate = false

    // 递归设置所有子节点
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((child) => {
        setNodeAndChildrenRecursive(child, checked)
      })
    }
  } catch (error) {
    console.error('设置节点状态失败:', error)
  }
}

// 向上更新父节点状态 - 添加循环引用保护
const updateParentChain = (node) => {
  if (!node || !node.parentId) return

  try {
    const visited = new Set()
    let currentNode = node

    while (currentNode && currentNode.parentId && !visited.has(currentNode.id)) {
      visited.add(currentNode.id)

      const parent = findNodeById(currentNode.parentId)
      if (!parent) break

      // 检查父节点的所有子节点状态
      const children = parent.children || []
      const checkedChildren = children.filter((child) => child && child.checked)
      const indeterminateChildren = children.filter((child) => child && child.indeterminate)

      if (checkedChildren.length === children.length && children.length > 0) {
        // 所有子节点都选中
        parent.checked = true
        parent.indeterminate = false
      } else if (checkedChildren.length > 0 || indeterminateChildren.length > 0) {
        // 部分子节点选中
        parent.checked = false
        parent.indeterminate = true
      } else {
        // 没有子节点选中
        parent.checked = false
        parent.indeterminate = false
      }

      currentNode = parent
    }
  } catch (error) {
    console.error('更新父节点链失败:', error)
  }
}

// 增量更新单个节点状态 - 优化性能
const updateSingleNodeState = (node) => {
  if (!node) return

  try {
    if (node.type === 'action') {
      permissionData.actionCheckedMap[node.id] = node.checked
    } else {
      // 更新对应的选中状态映射
      const stateMap = {
        module: permissionData.moduleCheckedMap,
        menu: permissionData.menuCheckedMap,
        submenu: permissionData.submenuCheckedMap,
      }[node.type]

      if (stateMap) {
        if (!stateMap[node.id]) {
          stateMap[node.id] = { checked: false, indeterminate: false }
        }
        stateMap[node.id].checked = node.checked
        stateMap[node.id].indeterminate = node.indeterminate
      }

      // 更新menuIds
      const index = permissionData.menuIds.indexOf(node.id)
      if (node.checked || node.indeterminate) {
        if (index === -1) {
          permissionData.menuIds.push(node.id)
        }
      } else {
        if (index !== -1) {
          permissionData.menuIds.splice(index, 1)
        }
      }
    }
  } catch (error) {
    console.error('更新单个节点状态失败:', error)
  }
}

// 同步树状态到响应式数据 - 优化性能，减少重复计算
const syncTreeToReactiveData = () => {
  try {
    // 清空现有数据
    permissionData.menuIds = []
    Object.keys(permissionData.actionCheckedMap).forEach((key) => {
      permissionData.actionCheckedMap[key] = false
    })

    // 遍历树收集选中状态
    const collectNodeStates = (node) => {
      if (!node) return

      updateSingleNodeState(node)

      // 递归处理子节点
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach((child) => collectNodeStates(child))
      }
    }

    if (permissionTree.value?.tree) {
      permissionTree.value.tree.forEach((rootNode) => collectNodeStates(rootNode))
    }

    // 去重menuIds
    permissionData.menuIds = [...new Set(permissionData.menuIds)]

    // 计算全选状态
    const rootNodes = permissionTree.value?.tree || []
    const allRootChecked = rootNodes.length > 0 && rootNodes.every((node) => node && node.checked)
    const someRootCheckedOrIndeterminate = rootNodes.some(
      (node) => node && (node.checked || node.indeterminate),
    )

    permissionData.allMenuChecked = allRootChecked
    permissionData.isIndeterminate = !allRootChecked && someRootCheckedOrIndeterminate
  } catch (error) {
    console.error('同步树状态失败:', error)
  }
}

// 统一的权限切换处理函数
const togglePermissionNode = (nodeId, checked = null) => {
  try {
    const node = findNodeById(nodeId)
    if (!node) {
      console.warn(`节点 ${nodeId} 未找到`)
      return
    }

    // 如果未指定checked，则切换当前状态
    const newChecked = checked !== null ? checked : !node.checked

    // 设置当前节点及所有子节点
    setNodeAndChildrenRecursive(node, newChecked)

    // 向上更新父节点状态
    updateParentChain(node)

    // 同步到响应式数据
    syncTreeToReactiveData()
  } catch (error) {
    console.error('切换权限节点失败:', error)
  }
}

// 初始化权限数据
const initPermissionData = () => {
  try {
    if (!props.currentRole) {
      resetPermissionData()
      return
    }

    // 先重置数据
    resetPermissionData()

    // 从角色获取权限列表
    const permissions = props.currentRole.permissions || []

    // 提取权限标识
    let permissionNames = []
    if (permissions.length > 0) {
      const firstPermission = permissions[0]

      if (typeof firstPermission === 'string') {
        permissionNames = permissions
      } else if (firstPermission && typeof firstPermission === 'object') {
        if ('name' in firstPermission) {
          permissionNames = permissions.map((p) => p.name)
        } else if ('permission' in firstPermission) {
          permissionNames = permissions.map((p) => p.permission)
        } else {
          const keys = Object.keys(firstPermission)
          if (keys.length > 0) {
            permissionNames = permissions.map((p) => p[keys[0]])
          }
        }
      }
    }

    // 遍历权限树，标记选中的权限
    const markPermissionsInTree = (node) => {
      if (!node) return

      // 检查当前节点是否有权限且被选中
      if (node.permission && permissionNames.includes(node.permission)) {
        node.checked = true
      }

      // 递归处理子节点
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach((child) => markPermissionsInTree(child))
      }

      // 如果有子节点被选中，更新当前节点状态
      const checkedChildren = (node.children || []).filter((child) => child && child.checked)
      const indeterminateChildren = (node.children || []).filter(
        (child) => child && child.indeterminate,
      )

      if (checkedChildren.length === node.children.length && node.children.length > 0) {
        node.checked = true
        node.indeterminate = false
      } else if (checkedChildren.length > 0 || indeterminateChildren.length > 0) {
        if (!node.checked) {
          // 如果节点本身没有被直接选中
          node.indeterminate = true
        }
      }
    }

    // 标记权限
    if (permissionTree.value?.tree) {
      permissionTree.value.tree.forEach((rootNode) => markPermissionsInTree(rootNode))
    }

    // 同步到响应式数据
    syncTreeToReactiveData()
  } catch (error) {
    console.error('初始化权限数据失败:', error)
  }
}

// 替换原有的处理函数
const handleCheckAllChange = (checked) => {
  try {
    if (permissionTree.value?.tree) {
      permissionTree.value.tree.forEach((moduleNode) => {
        togglePermissionNode(moduleNode.id, checked)
      })
    }
  } catch (error) {
    console.error('处理全选变更失败:', error)
  }
}

const handleModuleChange = (moduleId) => {
  togglePermissionNode(moduleId)
}

const handleMenuChange = (menuId) => {
  togglePermissionNode(menuId)
}

const handleSubmenuChange = (submenuId) => {
  togglePermissionNode(submenuId)
}

const handleActionChange = (actionId) => {
  togglePermissionNode(actionId)
}

// ==================== 优化后的 savePermission ====================

// 基于权限树收集选中的权限
const collectSelectedPermissions = () => {
  try {
    const selectedPermissions = new Set()

    // 递归遍历权限树，收集选中的权限
    const traverseNode = (node) => {
      if (!node) return

      // 如果节点被选中且有权限标识，收集该权限
      if (node.checked && node.permission) {
        selectedPermissions.add(node.permission)
      }

      // 递归处理子节点
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach((child) => traverseNode(child))
      }
    }

    // 遍历所有根节点
    if (permissionTree.value?.tree) {
      permissionTree.value.tree.forEach((rootNode) => traverseNode(rootNode))
    }

    // 确保父级权限被包含
    const ensureParentPermissions = () => {
      const allSelectedNodes = []

      // 收集所有选中的节点
      const collectSelectedNodes = (node) => {
        if (!node) return

        if (node.checked) {
          allSelectedNodes.push(node)
        }
        if (node.children && Array.isArray(node.children)) {
          node.children.forEach((child) => collectSelectedNodes(child))
        }
      }

      if (permissionTree.value?.tree) {
        permissionTree.value.tree.forEach((rootNode) => collectSelectedNodes(rootNode))
      }

      // 为每个选中的节点，确保其所有父级权限也被包含
      allSelectedNodes.forEach((node) => {
        const visited = new Set()
        let currentNode = node

        while (currentNode && currentNode.parentId && !visited.has(currentNode.parentId)) {
          visited.add(currentNode.id)
          const parentNode = findNodeById(currentNode.parentId)
          if (parentNode && parentNode.permission) {
            selectedPermissions.add(parentNode.permission)
          }
          currentNode = parentNode
        }
      })
    }

    // 确保父级权限完整
    ensureParentPermissions()

    return Array.from(selectedPermissions)
  } catch (error) {
    console.error('收集选中权限失败:', error)
    return []
  }
}

// 验证权限数据完整性
const validatePermissionData = (permissions) => {
  // 基本验证
  if (!Array.isArray(permissions)) {
    throw new Error('权限数据格式不正确')
  }

  // 验证权限格式
  const invalidPermissions = permissions.filter((p) => !p || typeof p !== 'string')
  if (invalidPermissions.length > 0) {
    throw new Error('权限数据包含无效项')
  }

  return true
}

// 成功处理
const handleSaveSuccess = (response, permissions) => {
  ElMessage.success('权限分配成功')

  // 可选：触发其他操作
  // emit('permission-saved', permissions)
}

// 错误处理
const handleSaveError = (error) => {
  console.error('权限保存失败:', error)

  // 根据错误类型提供不同的用户提示
  if (error.message.includes('权限数据')) {
    ElMessage.error(`数据验证失败: ${error.message}`)
  } else if (error.response?.status === 403) {
    ElMessage.error('没有权限执行此操作')
  } else if (error.response?.status === 422) {
    ElMessage.error('权限数据格式不正确，请检查后重试')
  } else {
    ElMessage.error('权限保存失败，请重试')
  }
}

// 优化后的保存权限函数
const savePermission = async () => {
  // 前置检查
  if (!props.currentRole) {
    ElMessage.warning('请先选择角色')
    return
  }

  emit('loading-change', true)

  try {
    // 1. 基于权限树收集权限数据（包含父级权限）
    const permissions = collectSelectedPermissions()

    // 2. 数据验证（确保数据正确性）
    validatePermissionData(permissions)

    // 3. 调用API（只提交permissions数组）
    const response = await assignPermissions(props.currentRole.id, { permissions })

    // 4. 统一的成功处理
    handleSaveSuccess(response, permissions)
  } catch (error) {
    // 5. 统一的错误处理
    handleSaveError(error)
  } finally {
    emit('loading-change', false)
  }
}

// 处理状态按钮点击
const handleStatusClick = (row) => {
  const currentStatus = row.status
  const targetStatus = currentStatus === 1 ? 0 : 1
  const text = currentStatus === 1 ? '停用' : '启用'

  ElMessageBox.confirm(`确认要${text}"${row.role_name}"角色吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      emit('loading-change', true)
      try {
        await updateRoleStatus(row.id)
        row.status = targetStatus
        ElMessage.success(`${text}成功`)
        emit('status-change', row)
      } catch (error) {
        console.error('修改状态失败', error)
        ElMessage.error('操作失败，请重试')
      } finally {
        emit('loading-change', false)
      }
    })
    .catch(() => {
      ElMessage.info('已取消操作')
    })
}

// 监听当前角色变化
watch(
  () => props.currentRole,
  (newVal) => {
    if (newVal) {
      initPermissionData()
    } else {
      resetPermissionData()
    }
  },
  { immediate: true },
)

// 组件卸载时清理资源 - 防止内存泄漏
onUnmounted(() => {
  clearPermissionTree()
})

// 暴露方法给父组件
defineExpose({
  savePermission,
  resetPermissionData,
  initPermissionData,
})
</script>

<template>
  <el-card class="permission-card" shadow="hover" v-loading="loading">
    <template #header>
      <div class="card-header">
        <span>权限配置 - {{ currentRole?.role_name || '未选择角色' }}</span>
        <div class="header-info" v-if="currentRole">
          <el-tag type="info">{{ currentRole.name }}</el-tag>
          <div class="status-container ml-2">
            <span class="status-label">状态:</span>
            <div class="status-display">
              <el-tag
                :type="currentRole.status === 1 ? 'success' : 'danger'"
                size="small"
                class="mr-2"
              >
                {{ currentRole.status === 1 ? '正常' : '停用' }}
              </el-tag>
              <el-button
                type="primary"
                size="small"
                :loading="loading"
                :disabled="loading"
                @click="handleStatusClick(currentRole)"
              >
                {{ currentRole.status === 1 ? '停用' : '启用' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <div class="permission-content" v-if="currentRole">
      <div class="permission-header">
        <el-checkbox
          v-model="permissionData.allMenuChecked"
          :indeterminate="permissionData.isIndeterminate"
          @change="handleCheckAllChange"
          class="check-all"
        >
          全选
        </el-checkbox>

        <el-button type="primary" @click="savePermission">保存</el-button>
      </div>

      <div class="permission-divider"></div>

      <!-- 权限表格列表布局 -->
      <div class="permission-table">
        <!-- 表格内容 - 模块行 -->
        <div v-for="module in menuData" :key="module.id" class="permission-module-row">
          <!-- 模块单元格 -->
          <div class="permission-cell module-cell">
            <el-checkbox
              :model-value="
                permissionData.moduleCheckedMap[module.id] &&
                permissionData.moduleCheckedMap[module.id].checked
              "
              :indeterminate="
                permissionData.moduleCheckedMap[module.id] &&
                permissionData.moduleCheckedMap[module.id].indeterminate
              "
              @change="() => handleModuleChange(module.id)"
              class="module-checkbox"
            >
              {{ module.label }}
            </el-checkbox>
          </div>

          <!-- 菜单列表容器 -->
          <div class="menu-container">
            <!-- 遍历菜单并在每个菜单后面立即显示其子菜单 -->
            <template v-for="menu in module.children" :key="menu.id">
              <!-- 显示菜单 -->
              <div class="permission-menu-row">
                <!-- 菜单单元格 -->
                <div class="permission-cell menu-cell">
                  <el-checkbox
                    :model-value="
                      permissionData.menuCheckedMap[menu.id] &&
                      permissionData.menuCheckedMap[menu.id].checked
                    "
                    :indeterminate="
                      permissionData.menuCheckedMap[menu.id] &&
                      permissionData.menuCheckedMap[menu.id].indeterminate
                    "
                    @change="() => handleMenuChange(menu.id)"
                    class="menu-checkbox"
                  >
                    {{ menu.label }}
                  </el-checkbox>
                </div>

                <!-- 操作权限单元格 - 处理第二级菜单 -->
                <div
                  v-if="menu.actions && Array.isArray(menu.actions)"
                  class="permission-cell action-cell"
                >
                  <div class="action-list">
                    <div v-for="action in menu.actions" :key="action.id" class="action-item">
                      <el-checkbox
                        :model-value="permissionData.actionCheckedMap[action.id]"
                        @change="() => handleActionChange(action.id)"
                        class="action-checkbox"
                      >
                        {{ action.label }}
                      </el-checkbox>
                    </div>
                  </div>
                </div>

                <!-- 如果是三级菜单，则为空占位 -->
                <div v-else class="permission-cell action-cell"></div>
              </div>

              <!-- 如果有子菜单，立即在此菜单下方显示 -->
              <template v-if="menu.children && Array.isArray(menu.children)">
                <!-- 子菜单列表 -->
                <div
                  v-for="submenu in menu.children"
                  :key="submenu.id"
                  class="permission-submenu-row"
                >
                  <!-- 子菜单单元格 - 缩进显示 -->
                  <div class="permission-cell menu-cell submenu-cell">
                    <div class="submenu-indent">
                      <el-checkbox
                        :model-value="
                          permissionData.submenuCheckedMap[submenu.id] &&
                          permissionData.submenuCheckedMap[submenu.id].checked
                        "
                        :indeterminate="
                          permissionData.submenuCheckedMap[submenu.id] &&
                          permissionData.submenuCheckedMap[submenu.id].indeterminate
                        "
                        @change="() => handleSubmenuChange(submenu.id)"
                        class="submenu-checkbox"
                      >
                        {{ submenu.label }}
                      </el-checkbox>
                    </div>
                  </div>

                  <!-- 子菜单操作权限单元格 -->
                  <div class="permission-cell action-cell">
                    <div class="action-list">
                      <div
                        v-if="submenu.actions && Array.isArray(submenu.actions)"
                        v-for="action in submenu.actions"
                        :key="action.id"
                        class="action-item"
                      >
                        <el-checkbox
                          :model-value="permissionData.actionCheckedMap[action.id]"
                          @change="() => handleActionChange(action.id)"
                          class="action-checkbox"
                        >
                          {{ action.label }}
                        </el-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </div>
      </div>
    </div>

    <div class="empty-placeholder" v-else>
      <el-empty description="请先选择一个角色" />
    </div>
  </el-card>
</template>

<style scoped>
.permission-card {
  border-radius: 8px;
  height: calc(100vh - 120px);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 500;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f5f7fa;
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.status-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.permission-content {
  height: calc(100% - 56px);
  overflow-y: auto;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 10;
  padding: 10px 0 16px 0;
}

.permission-divider {
  height: 1px;
  background-color: #ebeef5;
  margin-bottom: 20px;
  width: 100%;
}

.permission-table {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
  overflow-y: auto;
}

.permission-module-row {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
  border-radius: 0 0 8px 8px;
}

.permission-module-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.permission-cell {
  padding: 8px 12px;
}

.module-cell {
  flex: 1;
  background-color: #f6f8fa;
  border-radius: 8px 0 0 8px;
  padding: 12px 15px;
  font-weight: 500;
}

.menu-cell {
  flex: 2;
}

.action-cell {
  flex: 3;
}

.menu-container {
  flex: 5;
  display: flex;
  flex-direction: column;
}

.permission-menu-row {
  display: flex;
  margin-bottom: 12px;
  padding: 8px 0;
  border-radius: 8px;
  align-items: flex-start;
  transition: background-color 0.2s ease;
}

.permission-menu-row:hover {
  background-color: #f5f7fa;
}

.permission-parent-menu {
  margin: 15px 0 10px 20px;
  color: #606266;
  font-weight: 500;
  display: none;
  /* 隐藏菜单管理说明 */
}

.parent-menu-label {
  position: relative;
  display: inline-block;
  padding-left: 8px;
  border-left: 3px solid var(--el-color-primary);
  font-size: 14px;
}

.permission-submenu-row {
  display: flex;
  margin: 8px 0 10px 20px;
  padding: 8px 10px;
  border-radius: 8px;
  align-items: flex-start;
  background-color: #f9f9f9;
  transition: background-color 0.2s ease;
}

.permission-submenu-row:hover {
  background-color: #f0f2f5;
}

.submenu-indent {
  position: relative;
  padding-left: 25px;
}

.submenu-indent:before {
  display: none;
  /* 移除连接线 */
}

.action-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-item {
  margin-right: 15px;
}

.module-checkbox {
  font-size: 16px;
  font-weight: 500;
}

.menu-checkbox {
  font-weight: 500;
}

.submenu-checkbox {
  font-weight: 500;
  font-size: 14px;
  color: #606266;
}

.action-checkbox {
  font-size: 13px;
  white-space: nowrap;
}

.empty-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.check-all {
  font-size: 16px;
  font-weight: bold;
}

/* 优化滚动条样式 */
.permission-content::-webkit-scrollbar {
  width: 6px;
}

.permission-content::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.permission-content::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

/* 为权限表格添加与左侧角色列表一致的滚动条样式 */
.permission-table::-webkit-scrollbar {
  width: 6px;
}

.permission-table::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.permission-table::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

/* 确保卡片内容正确滚动 */
:deep(.el-card__body) {
  height: calc(100% - 56px);
  overflow: hidden;
  padding: 0;
}

:deep(.permission-card .el-card__body) {
  display: flex;
  flex-direction: column;
}

:deep(.permission-card .el-card__header) {
  position: sticky;
  top: 0;
  z-index: 12;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
}

.permission-card :deep(.el-divider) {
  margin: 0;
  position: sticky;
  top: 50px;
  z-index: 9;
}
</style>
