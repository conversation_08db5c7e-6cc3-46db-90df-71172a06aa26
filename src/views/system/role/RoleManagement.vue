<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getRoleList, deleteRole } from '@/api/role'
import { getMenuTree } from '@/api/menu'
import RoleList from './components/RoleList.vue'
import RoleForm from './components/RoleForm.vue'
import PermissionConfig from './components/PermissionConfig.vue'

// 角色管理页面

// 表格数据
const roleList = ref([])

// 表格加载状态
const loading = ref(false)

// 当前选中的角色
const currentRole = ref(null)

// 菜单数据
const menuData = ref([])

// 菜单数据是否已加载
const menuLoaded = ref(false)

// 角色数据是否已加载
const rolesLoaded = ref(false)

// 角色表单对话框
const roleDialog = reactive({
  visible: false,
  title: '',
})

// 编辑的角色数据
const editingRole = ref(null)

// 权限配置引用
const permissionConfigRef = ref(null)

// 获取菜单树数据并转换为权限配置所需格式
const getMenuData = async () => {
  try {
    loading.value = true
    const res = await getMenuTree()
    if (res.code === 200 && res.data) {
      console.log('API返回的原始菜单数据:', res.data)

      // 将API返回的菜单树转换为组件需要的格式
      menuData.value = formatMenuData(res.data)

      console.log('转换后的菜单数据:', menuData.value)

      // 标记菜单数据已加载
      menuLoaded.value = true

      // 如果角色数据也已加载，初始化权限配置
      if (rolesLoaded.value && currentRole.value && permissionConfigRef.value) {
        console.log('两个数据源都已加载，初始化权限配置', {
          roleId: currentRole.value.id,
          roleName: currentRole.value.role_name,
          menuCount: menuData.value.length
        })
        nextTick(() => {
          permissionConfigRef.value.resetPermissionData()
          permissionConfigRef.value.initPermissionData()
        })
      }
    } else {
      ElMessage.error(res.message || '获取菜单数据失败')
    }
  } catch (error) {
    console.error('获取菜单数据失败', error)
    ElMessage.error('获取菜单数据失败')
  } finally {
    loading.value = false
  }
}

// 格式化菜单数据为权限配置所需格式
const formatMenuData = (menus) => {
  if (!menus || !Array.isArray(menus)) return []

  return menus.map(menu => {
    const formattedMenu = {
      id: menu.id,
      label: menu.hidden ? `${menu.name}(隐藏页面)` : menu.name,  // 标识隐藏页面
      children: [],
      // 保存菜单自身的权限标识
      permission: menu.permission
    }

    // 如果有子菜单，递归处理
    if (menu.children && menu.children.length > 0) {
      // 检查子菜单类型，区分菜单和按钮
      const submenus = menu.children.filter(item => item.type === 0)
      const buttons = menu.children.filter(item => item.type === 1)

      // 如果有菜单类型的子项，递归处理
      if (submenus.length > 0) {
        formattedMenu.children = formatMenuData(submenus)
      }

      // 如果有按钮类型的子项，添加为actions
      if (buttons.length > 0) {
        formattedMenu.actions = buttons.map(btn => ({
          // 使用 "父ID:权限标识" 的格式作为唯一权限ID
          id: `${btn.parent_id}:${btn.permission || btn.id}`,
          label: btn.name,
          // 保存原始权限标识，用于提交给API
          permission: btn.permission
        }))
      }
    }

    return formattedMenu
  })
}

// 获取角色列表
const getList = async (keepSelection = false) => {
  loading.value = true
  let currentSelectedRoleId = null;

  // 保存当前选中的角色ID
  if (keepSelection && currentRole.value) {
    currentSelectedRoleId = currentRole.value.id;
  }

  try {
    const res = await getRoleList()
    if (res.code === 200 && res.data) {
      // API返回格式: res.data.data是角色数组
      if (res.data.data && Array.isArray(res.data.data)) {
        // 转换API返回的数据结构为组件使用的格式
        roleList.value = res.data.data.map(item => ({
          id: item.id,
          name: item.name,
          role_name: item.role_name,
          guard_name: item.guard_name || 'web',
          sort: item.sort,
          status: typeof item.status === 'string' ? parseInt(item.status) : item.status, // 确保状态是数字
          createTime: item.created_at,
          remark: item.remark,
          permissions: item.permissions || []
        }))

        // 如果需要保持选中状态
        if (keepSelection && currentSelectedRoleId) {
          // 在新数据中查找之前选中的角色
          const selectedRole = roleList.value.find(role => role.id === currentSelectedRoleId);
          if (selectedRole) {
            // 找到了，继续选中
            currentRole.value = selectedRole;
            // 提前返回，避免默认选中第一个

            // 标记角色数据已加载
            rolesLoaded.value = true

            // 如果菜单数据也已加载，初始化权限配置
            if (menuLoaded.value && permissionConfigRef.value) {
              console.log('两个数据源都已加载，初始化权限配置', {
                roleId: currentRole.value.id,
                roleName: currentRole.value.role_name,
                menuCount: menuData.value.length
              })
              nextTick(() => {
                permissionConfigRef.value.resetPermissionData()
                permissionConfigRef.value.initPermissionData()
              })
            }

            return;
          }
        }
      } else {
        roleList.value = []
        console.warn('API返回的角色列表结构不符合预期', res.data)
      }
    } else {
      roleList.value = []
      ElMessage.error(res.message || '获取角色列表失败')
    }

    // 默认选中第一个角色（当找不到之前选中的角色时）
    if (roleList.value.length > 0) {
      currentRole.value = roleList.value[0]
    } else {
      currentRole.value = null
    }

    // 标记角色数据已加载
    rolesLoaded.value = true

    // 如果菜单数据也已加载，初始化权限配置
    if (menuLoaded.value && currentRole.value && permissionConfigRef.value) {
      console.log('两个数据源都已加载，初始化权限配置', {
        roleId: currentRole.value.id,
        roleName: currentRole.value.role_name,
        menuCount: menuData.value.length
      })
      nextTick(() => {
        permissionConfigRef.value.resetPermissionData()
        permissionConfigRef.value.initPermissionData()
      })
    }
  } catch (error) {
    console.error('获取角色列表失败', error)
    ElMessage.error('获取角色列表失败')
    roleList.value = []
    currentRole.value = null
  } finally {
    loading.value = false
  }
}

// 选择角色
const handleSelectRole = (row) => {
  currentRole.value = row

  // 当选择角色变更时，初始化权限配置
  if (permissionConfigRef.value) {
    // 使用nextTick确保在DOM更新后执行
    nextTick(() => {
      permissionConfigRef.value.resetPermissionData()
      permissionConfigRef.value.initPermissionData()
    })
  }
}

// 新增角色
const handleAdd = () => {
  roleDialog.visible = true
  roleDialog.title = '添加角色'
  editingRole.value = null
}

// 编辑角色
const handleEdit = (row) => {
  roleDialog.visible = true
  roleDialog.title = '编辑角色'
  editingRole.value = row
}

// 删除角色
const handleDelete = async (row) => {
  loading.value = true
  try {
    await deleteRole(row.id)

    // 如果删除的是当前选中角色，重置选中状态
    if (currentRole.value && currentRole.value.id === row.id) {
      currentRole.value = null
    }

    // 刷新列表，如果当前选中的角色不是被删除的角色，保持选中状态
    getList(currentRole.value && currentRole.value.id !== row.id)
    ElMessage.success('删除成功')
  } catch (error) {
    ElMessage.error('删除失败，请重试')
  } finally {
    loading.value = false
  }
}

// 表单提交成功处理
const handleFormSuccess = (roleId) => {
  // 刷新列表后，尝试选中刚才操作的角色
  getList(false) // 先不保持选中，获取新数据

  // 找到刚操作的角色并选中
  if (roleId) {
    setTimeout(() => {
      const targetRole = roleList.value.find(role => role.id === roleId)
      if (targetRole) {
        currentRole.value = targetRole
      }
    }, 100)
  }
}

// 角色状态变更处理
const handleStatusChange = (role) => {
  // 刷新列表，并保持当前选中状态
  getList(true)
}

// 设置加载状态
const setLoading = (isLoading) => {
  loading.value = isLoading
}

// 初始化
onMounted(() => {
  getList()
  getMenuData()
})
</script>

<template>
  <div class="role-container">
    <el-row :gutter="20">
      <!-- 左侧角色列表 -->
      <el-col :span="7">
        <RoleList :loading="loading" :roleList="roleList" :currentRole="currentRole" @select="handleSelectRole"
          @add="handleAdd" @edit="handleEdit" @delete="handleDelete" />
      </el-col>

      <!-- 右侧权限配置 -->
      <el-col :span="17">
        <PermissionConfig ref="permissionConfigRef" :loading="loading" :currentRole="currentRole" :menuData="menuData"
          @status-change="handleStatusChange" @loading-change="setLoading" />
      </el-col>
    </el-row>

    <!-- 角色表单对话框 -->
    <RoleForm :visible="roleDialog.visible" @update:visible="roleDialog.visible = $event" :title="roleDialog.title"
      :roleData="editingRole" @success="handleFormSuccess" />
  </div>
</template>

<style scoped>
.role-container {
  padding: 20px;
  height: 100%;
}
</style>