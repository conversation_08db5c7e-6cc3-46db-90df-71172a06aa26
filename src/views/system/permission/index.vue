<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Search, Plus, Edit, Delete, Refresh, Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DateTypeRangePicker from '@/components/DateTypeRangePicker.vue'

// 查询条件
const queryParams = reactive({
  searchType: 'permName', // 默认搜索类型为权限名称
  searchValue: '', // 搜索值
  status: '',
  dateType: 'createTime', // 默认日期类型为创建时间
  dateRange: []
})

// 搜索类型选项
const searchTypeOptions = [
  { value: 'permName', label: '权限名称' },
  { value: 'permKey', label: '权限标识' }
]

// 日期类型选项
const dateTypeOptions = [
  { value: 'createTime', label: '创建时间' },
  { value: 'updateTime', label: '更新时间' }
]

// 表格数据
const permissionList = ref([
  { 
    id: 1, 
    permName: '用户查询', 
    permKey: 'system:user:query', 
    permType: '按钮', 
    status: '1',
    orderNum: 1, 
    createTime: '2023-01-01 10:00:00' 
  },
  { 
    id: 2, 
    permName: '用户新增', 
    permKey: 'system:user:add', 
    permType: '按钮', 
    status: '1',
    orderNum: 2, 
    createTime: '2023-01-01 10:05:00' 
  },
  { 
    id: 3, 
    permName: '用户修改', 
    permKey: 'system:user:edit', 
    permType: '按钮', 
    status: '1',
    orderNum: 3, 
    createTime: '2023-01-01 10:10:00' 
  },
  { 
    id: 4, 
    permName: '用户删除', 
    permKey: 'system:user:remove', 
    permType: '按钮', 
    status: '1',
    orderNum: 4, 
    createTime: '2023-01-01 10:15:00' 
  },
  { 
    id: 5, 
    permName: '角色查询', 
    permKey: 'system:role:query', 
    permType: '按钮', 
    status: '1',
    orderNum: 1, 
    createTime: '2023-01-01 11:00:00' 
  }
])

// 表格加载状态
const loading = ref(false)

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 状态字典
const statusOptions = [
  { value: '1', label: '正常' },
  { value: '0', label: '停用' }
]

// 权限类型选项
const permTypeOptions = [
  { value: '菜单', label: '菜单' },
  { value: '按钮', label: '按钮' },
  { value: '接口', label: '接口' }
]

// 权限表单对话框
const permissionDialog = reactive({
  visible: false,
  title: '',
  loading: false
})

// 权限表单
const permissionForm = reactive({
  id: undefined,
  permName: '',
  permKey: '',
  permType: '按钮',
  status: '1',
  orderNum: 0,
  description: ''
})

// 表单校验规则
const permissionFormRules = {
  permName: [
    { required: true, message: '权限名称不能为空', trigger: 'blur' }
  ],
  permKey: [
    { required: true, message: '权限标识不能为空', trigger: 'blur' }
  ],
  permType: [
    { required: true, message: '权限类型不能为空', trigger: 'blur' }
  ],
  orderNum: [
    { required: true, message: '显示顺序不能为空', trigger: 'blur' }
  ]
}

const permissionFormRef = ref(null)

// 搜索
const handleQuery = () => {
  loading.value = true
  // 这里应该调用API接口获取数据
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询成功')
  }, 500)
}

// 重置搜索
const resetQuery = () => {
  queryParams.searchType = 'permName'
  queryParams.searchValue = ''
  queryParams.status = ''
  queryParams.dateType = 'createTime'
  queryParams.dateRange = []
  handleQuery()
}

// 新增权限
const handleAdd = () => {
  permissionDialog.visible = true
  permissionDialog.title = '添加权限'
  resetForm()
}

// 编辑权限
const handleEdit = (row) => {
  permissionDialog.visible = true
  permissionDialog.title = '编辑权限'
  resetForm()
  
  // 模拟异步获取权限详情
  setTimeout(() => {
    permissionForm.id = row.id
    permissionForm.permName = row.permName
    permissionForm.permKey = row.permKey
    permissionForm.permType = row.permType
    permissionForm.status = row.status
    permissionForm.orderNum = row.orderNum
    permissionForm.description = row.description || ''
  }, 100)
}

// 提交表单
const submitForm = async () => {
  if (!permissionFormRef.value) return
  
  try {
    await permissionFormRef.value.validate()
    permissionDialog.loading = true
    
    // 模拟提交
    setTimeout(() => {
      permissionDialog.loading = false
      permissionDialog.visible = false
      ElMessage.success(permissionForm.id ? '修改成功' : '新增成功')
      handleQuery()
    }, 600)
  } catch (error) {
    console.error('表单校验失败', error)
  }
}

// 重置表单
const resetForm = () => {
  permissionForm.id = undefined
  permissionForm.permName = ''
  permissionForm.permKey = ''
  permissionForm.permType = '按钮'
  permissionForm.status = '1'
  permissionForm.orderNum = 0
  permissionForm.description = ''
  
  if (permissionFormRef.value) {
    permissionFormRef.value.resetFields()
  }
}

// 删除权限
const handleDelete = (row) => {
  const permissionIds = row.id ? [row.id] : selectedRows.value.map(item => item.id)
  
  ElMessageBox.confirm(`确认删除选中的${permissionIds.length}条数据?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟删除
    setTimeout(() => {
      ElMessage.success('删除成功')
      handleQuery()
    }, 500)
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 导出权限
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 切换权限状态
const handleStatusChange = (row) => {
  const text = row.status === '1' ? '停用' : '启用'
  const newStatus = row.status === '1' ? '0' : '1'
  
  ElMessageBox.confirm(`确认要${text}"${row.permName}"权限吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟调用接口修改状态
    loading.value = true
    setTimeout(() => {
      row.status = newStatus
      loading.value = false
      ElMessage.success(`${text}成功`)
    }, 500)
  }).catch(() => {
    // 取消操作，回滚状态
    row.status = row.status === '1' ? '0' : '1'
    ElMessage.info('已取消操作')
  })
}

// 初始化
onMounted(() => {
  try {
    handleQuery()
  } catch (error) {
    console.error('权限管理初始化失败:', error)
  }
})
</script>

<template>
  <div class="permission-container">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item prop="searchValue" class="search-item">
          <el-input v-model="queryParams.searchValue" placeholder="请输入搜索内容" clearable style="width: 240px" class="input-with-select">
            <template #prepend>
              <el-select v-model="queryParams.searchType" style="width: 110px">
                <el-option 
                  v-for="option in searchTypeOptions" 
                  :key="option.value" 
                  :label="option.label" 
                  :value="option.value" 
                />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="status" class="search-item">
          <el-select v-model="queryParams.status" placeholder="权限状态" clearable style="width: 120px">
            <el-option 
              v-for="dict in statusOptions" 
              :key="dict.value" 
              :label="dict.label" 
              :value="dict.value" 
            >
              <template #default>
                <span style="display: flex; align-items: center;">
                  <el-tag :type="dict.value === '1' ? 'success' : 'danger'" size="small" style="margin-right: 8px;">
                    {{ dict.label }}
                  </el-tag>
                  {{ dict.value === '1' ? '开启状态' : '禁用状态' }}
                </span>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="dateRange" class="search-item">
          <DateTypeRangePicker 
            v-model:dateType="queryParams.dateType"
            v-model:dateRange="queryParams.dateRange"
            :dateTypeOptions="dateTypeOptions"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
          <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>权限列表</span>
          <div class="right-buttons">
            <el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
            <el-button 
              type="danger" 
              :icon="Delete" 
              :disabled="selectedRows.length === 0"
              @click="handleDelete({})"
            >
              删除
            </el-button>
            <el-button type="success" :icon="Download" @click="handleExport">导出</el-button>
          </div>
        </div>
      </template>
  
      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="permissionList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="权限编号" prop="id" width="80" align="center" />
        <el-table-column label="权限名称" prop="permName" :show-overflow-tooltip="true" />
        <el-table-column label="权限标识" prop="permKey" :show-overflow-tooltip="true" />
        <el-table-column label="权限类型" prop="permType" align="center" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.permType === '按钮' ? 'info' : 'success'"
            >
              {{scope.row.permType}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="显示顺序" prop="orderNum" width="100" align="center" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="1"
              inactive-value="0"
              @change="handleStatusChange(scope.row)"
              :active-text="scope.row.status === '1' ? '正常' : '停用'"
              :inactive-text="scope.row.status === '1' ? '正常' : '停用'"
              inline-prompt
              class="status-switch"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" width="180">
          <template #default="scope">
            <el-button type="primary" link :icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :total="permissionList.length"
          :page-size="10"
          @size-change="handleQuery"
          @current-change="handleQuery"
        />
      </div>
    </el-card>
    
    <!-- 权限表单对话框 -->
    <el-dialog 
      :title="permissionDialog.title" 
      v-model="permissionDialog.visible" 
      width="500px" 
      append-to-body 
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form 
        ref="permissionFormRef" 
        :model="permissionForm" 
        :rules="permissionFormRules" 
        label-width="100px"
      >
        <el-form-item label="权限名称" prop="permName">
          <el-input v-model="permissionForm.permName" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限标识" prop="permKey">
          <el-input v-model="permissionForm.permKey" placeholder="请输入权限标识">
            <template #description>
              <span class="form-description">例如：system:user:list</span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="权限类型" prop="permType">
          <el-select v-model="permissionForm.permType" placeholder="请选择权限类型" style="width: 100%">
            <el-option 
              v-for="option in permTypeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input-number v-model="permissionForm.orderNum" controls-position="right" :min="0" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="权限状态" prop="status">
          <el-radio-group v-model="permissionForm.status">
            <el-radio 
              v-for="dict in statusOptions" 
              :key="dict.value" 
              :label="dict.value"
            >
              {{dict.label}}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input 
            v-model="permissionForm.description" 
            type="textarea" 
            placeholder="请输入权限描述" 
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialog.visible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="submitForm" 
            :loading="permissionDialog.loading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.permission-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.table-card {
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 500;
}

.right-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.status-switch :deep(.el-switch__core) {
  width: 55px !important;
}

/* 自定义搜索表单样式 */
.search-item {
  margin-right: 10px;
}

.input-with-select .el-input-group__prepend {
  background-color: var(--el-fill-color-blank);
  padding: 0;
}

.form-description {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
</style> 