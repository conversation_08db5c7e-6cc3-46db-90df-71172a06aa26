<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import IconSelector from './IconSelector.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '添加菜单'
  },
  loading: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  menuOptions: {
    type: Array,
    default: () => []
  },
  menuTypeOptions: {
    type: Array,
    default: () => []
  },
  statusOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 菜单表单
const menuForm = reactive({
  id: undefined,
  parent_id: 0,
  name: '',
  icon: '',
  type: 0,
  sort: 0,
  permission: '',
  path: '',
  component: '',
  status: 1,
  hidden: 0,
  redirect: ''
})

// 验证规则
const menuFormRules = computed(() => {
  const rules = {
    name: [
      { required: true, message: '菜单名称不能为空', trigger: 'blur' }
    ],
    type: [
      { required: true, message: '菜单类型不能为空', trigger: 'change' }
    ],
    status: [
      { required: true, message: '菜单状态不能为空', trigger: 'change' }
    ],
    sort: [
      { required: true, message: '显示顺序不能为空', trigger: 'blur' }
    ]
  }

  // 只有在显示权限标识字段时才添加验证规则
  if (showPerms(menuForm.type)) {
    rules.permission = [
      { required: true, message: '权限标识不能为空', trigger: 'blur' }
    ]
  }

  return rules
})

// 菜单表单引用
const menuFormRef = ref(null)

// 图标选择器引用
const iconSelectorRef = ref(null)

// 重置表单 - 移到 watch 之前声明
const resetForm = () => {
  // 重置所有字段为默认值
  menuForm.id = undefined
  menuForm.parent_id = 0
  menuForm.name = ''
  menuForm.icon = ''
  menuForm.type = 0
  menuForm.sort = 0
  menuForm.permission = ''
  menuForm.path = ''
  menuForm.component = ''
  menuForm.status = 1
  menuForm.hidden = 0
  menuForm.redirect = ''

  // 重置表单验证状态
  if (menuFormRef.value) {
    menuFormRef.value.clearValidate()
  }
}

// 监听表单数据变化
watch(() => props.formData, (newVal) => {
  if (newVal) {
    // 先清空所有字段
    resetForm()
    // 再填充新数据
    Object.keys(menuForm).forEach(key => {
      if (newVal[key] !== undefined) {
        menuForm[key] = newVal[key]
      }
    })
  }
}, { deep: true, immediate: true })

// 监听可见性
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 对话框关闭时重置表单
    resetForm()
  }
})

// 提交表单
const submitForm = async () => {
  if (!menuFormRef.value) return

  try {
    await menuFormRef.value.validate()
    emit('submit', { ...menuForm })
  } catch (error) {
    console.error('表单校验失败', error)
  }
}

// 打开图标选择器
const openIconSelect = () => {
  if (iconSelectorRef.value) {
    iconSelectorRef.value.openIconSelect()
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 根据菜单类型过滤表单项
const showComponent = (type) => type === 0
const showPerms = (type) => type === 0 || type === 1
const showIcon = (type) => type === 0
const showCache = (type) => type === 0

// 暴露方法给父组件
defineExpose({
  resetForm
})
</script>

<template>
  <el-dialog :title="title" :modelValue="visible" @update:modelValue="$emit('update:visible', $event)" width="680px"
    append-to-body destroy-on-close :close-on-click-modal="false" @close="handleClose">
    <el-form ref="menuFormRef" :model="menuForm" :rules="menuFormRules" label-width="100px">
      <el-form-item label="上级菜单">
        <el-tree-select v-model="menuForm.parent_id" :data="menuOptions"
          :props="{ label: 'name', value: 'id', children: 'children' }" value-key="id" placeholder="选择上级菜单"
          check-strictly style="width: 100%" />
      </el-form-item>
      <el-form-item label="菜单类型" prop="type">
        <el-radio-group v-model="menuForm.type">
          <el-radio v-for="option in menuTypeOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="menuForm.name" placeholder="请输入菜单名称" />
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="14">
          <el-form-item v-if="showIcon(menuForm.type)" label="菜单图标" prop="icon">
            <IconSelector ref="iconSelectorRef" v-model="menuForm.icon" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="menuForm.sort" controls-position="right" :min="0" style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="路由地址" prop="path">
        <el-input v-model="menuForm.path" placeholder="请输入路由地址" />
      </el-form-item>

      <el-form-item label="组件路径" prop="component" v-if="showComponent(menuForm.type)">
        <el-input v-model="menuForm.component" placeholder="请输入组件路径" />
      </el-form-item>

      <el-form-item label="权限标识" prop="permission" v-if="showPerms(menuForm.type)">
        <el-input v-model="menuForm.permission" placeholder="请输入权限标识（必填）" maxlength="100">
          <template #description>
            <span class="form-description">例如：system:user:list（必填字段，用于权限控制）</span>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="重定向" prop="redirect" v-if="showComponent(menuForm.type)">
        <el-input v-model="menuForm.redirect" placeholder="请输入重定向路径" />
      </el-form-item>

      <el-form-item label="显示状态" prop="hidden" v-if="menuForm.type === 0">
        <el-radio-group v-model="menuForm.hidden">
          <el-radio :value="0">显示在菜单</el-radio>
          <el-radio :value="1">隐藏页面</el-radio>
        </el-radio-group>
        <div class="form-description" style="margin-top: 4px;">
          <span>隐藏页面不会在侧边栏显示，但可以通过路由直接访问，且不能添加子菜单</span>
        </div>
      </el-form-item>

      <el-form-item label="菜单状态" prop="status">
        <el-radio-group v-model="menuForm.status">
          <el-radio v-for="dict in statusOptions" :key="dict.value" :value="dict.value">
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.form-description {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
</style>