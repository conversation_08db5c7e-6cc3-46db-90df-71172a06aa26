<script setup>
import { ref, reactive, computed, markRaw } from 'vue'
import { Search } from '@element-plus/icons-vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

// Element Plus 图标列表
const iconList = ref([])

// 初始化图标列表
const initIconList = () => {
  iconList.value = Object.keys(ElementPlusIconsVue).map(iconName => {
    return {
      name: iconName,
      component: markRaw(ElementPlusIconsVue[iconName])
    }
  })
}

// 图标选择器对话框
const iconSelectDialog = reactive({
  visible: false,
  searchValue: ''
})

// 搜索图标
const filteredIconList = computed(() => {
  const searchValue = iconSelectDialog.searchValue.toLowerCase()
  if (!searchValue) return iconList.value
  return iconList.value.filter(icon => icon.name.toLowerCase().includes(searchValue))
})

// 选择图标
const handleSelectIcon = (iconName) => {
  emit('update:modelValue', iconName)
  iconSelectDialog.visible = false
  iconSelectDialog.searchValue = ''
}

// 打开图标选择器
const openIconSelect = () => {
  iconSelectDialog.visible = true
  iconSelectDialog.searchValue = ''
}

// 预览当前选择的图标
const previewIcon = computed(() => {
  if (!props.modelValue) return null
  if (ElementPlusIconsVue[props.modelValue]) {
    return markRaw(ElementPlusIconsVue[props.modelValue])
  }
  return null
})

// 暴露方法给父组件
defineExpose({
  openIconSelect
})

// 初始化
initIconList()
</script>

<template>
  <div class="icon-selector">
    <el-input :modelValue="modelValue" placeholder="请选择菜单图标" @update:modelValue="emit('update:modelValue', $event)">
      <template #prepend v-if="modelValue">
        <div class="icon-preview">
          <el-icon>
            <component :is="previewIcon" />
          </el-icon>
        </div>
      </template>
      <template #append>
        <el-button :icon="Search" @click="openIconSelect">选择</el-button>
      </template>
    </el-input>

    <!-- 图标选择器对话框 -->
    <el-dialog
      title="选择图标"
      v-model="iconSelectDialog.visible"
      width="800px"
      append-to-body
      destroy-on-close
    >
      <div class="icon-search">
        <el-input
          v-model="iconSelectDialog.searchValue"
          placeholder="搜索图标"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="icon-grid">
        <div
          v-for="icon in filteredIconList"
          :key="icon.name"
          class="icon-item"
          @click="handleSelectIcon(icon.name)"
        >
          <div class="icon-wrapper">
            <component :is="icon.component" />
          </div>
          <span class="icon-name">{{ icon.name }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin-right: 5px;
}

.icon-search {
  margin-bottom: 20px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  max-height: 450px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  background-color: var(--el-fill-color-light);
  color: var(--el-color-primary);
}

.icon-wrapper {
  font-size: 22px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  width: 36px;
}

.icon-name {
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 90px;
}
</style> 