<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Search, Plus, Edit, Delete, Refresh } from '@element-plus/icons-vue'
import { Folder, Document, Operation } from '@element-plus/icons-vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// import DateTypeRangePicker from '@/components/DateTypeRangePicker.vue'
import { getMenuTree, addMenu, editMenu, deleteMenu, changeStatus } from '@/api/menu'
import MenuForm from './components/MenuForm.vue'

// 查询条件
const queryParams = reactive({
  searchType: 'name', // 默认搜索类型为菜单名称
  searchValue: '', // 搜索值
  status: '',
  dateType: 'created_at', // 默认日期类型为创建时间
  dateRange: []
})

// 搜索类型选项
const searchTypeOptions = [
  { value: 'name', label: '菜单名称' },
  { value: 'permission', label: '权限标识' },
  { value: 'path', label: '路由地址' }
]

// 日期类型选项
const dateTypeOptions = [
  { value: 'created_at', label: '创建时间' },
  { value: 'updated_at', label: '更新时间' }
]

// 菜单类型选项
const menuTypeOptions = [
  { value: 0, label: '菜单' },
  { value: 1, label: '按钮' }
]

// 原始菜单数据（未过滤）
const originalMenuList = ref([])

// 过滤后的菜单数据
const menuList = computed(() => {
  if (!originalMenuList.value || originalMenuList.value.length === 0) {
    return []
  }

  // 如果没有任何搜索条件，直接返回原始数据
  if (!queryParams.searchValue && queryParams.status === '' && queryParams.dateRange.length === 0) {
    return originalMenuList.value
  }

  return filterMenus(originalMenuList.value)
})

// 递归过滤菜单数据
const filterMenus = (menus) => {
  if (!menus || !Array.isArray(menus)) return []

  const result = []

  for (const menu of menus) {
    // 检查菜单是否符合搜索条件
    if (matchSearchCondition(menu)) {
      // 如果当前菜单符合条件，复制一份并添加到结果中
      const newMenu = { ...menu }

      // 如果有子菜单，则递归过滤子菜单
      if (newMenu.children && newMenu.children.length > 0) {
        newMenu.children = filterMenus(newMenu.children)
      }

      result.push(newMenu)
    } else if (menu.children && menu.children.length > 0) {
      // 如果当前菜单不符合条件，但其子菜单可能符合，则递归过滤子菜单
      const filteredChildren = filterMenus(menu.children)

      // 如果子菜单过滤后仍有数据，则需要保留父菜单
      if (filteredChildren.length > 0) {
        const newMenu = { ...menu }
        newMenu.children = filteredChildren
        result.push(newMenu)
      }
    }
  }

  return result
}

// 检查菜单是否符合搜索条件
const matchSearchCondition = (menu) => {
  // 1. 检查搜索值
  if (queryParams.searchValue) {
    const searchValue = queryParams.searchValue.toLowerCase()

    // 根据搜索类型进行匹配
    if (queryParams.searchType === 'name' &&
      (!menu.name || !menu.name.toLowerCase().includes(searchValue))) {
      return false
    }

    if (queryParams.searchType === 'permission' &&
      (!menu.permission || !menu.permission.toLowerCase().includes(searchValue))) {
      return false
    }

    if (queryParams.searchType === 'path' &&
      (!menu.path || !menu.path.toLowerCase().includes(searchValue))) {
      return false
    }
  }

  // 2. 检查状态
  if (queryParams.status !== '' && menu.status !== parseInt(queryParams.status)) {
    return false
  }

  // 3. 检查日期范围
  if (queryParams.dateRange && queryParams.dateRange.length === 2) {
    const startDate = new Date(queryParams.dateRange[0])
    const endDate = new Date(queryParams.dateRange[1])
    endDate.setHours(23, 59, 59, 999) // 设置为当天的最后一毫秒

    const menuDate = new Date(menu[queryParams.dateType])

    if (menuDate < startDate || menuDate > endDate) {
      return false
    }
  }

  return true
}

// 表格加载状态
const loading = ref(false)

// 表格展开行配置
const expandRowKeys = ref([])

// 状态字典
const statusOptions = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
]

// 菜单表单对话框
const menuDialog = reactive({
  visible: false,
  title: '',
  loading: false
})

// 当前编辑/新增的菜单数据
const currentMenu = ref({})

// 获取菜单下拉树列表
const menuOptions = ref([
  { id: 0, name: '主类目', children: [] }
])

// 菜单表单引用
const menuFormRef = ref(null)

// 新增菜单
const handleAdd = (row) => {
  // 完全重置表单数据
  currentMenu.value = {
    id: undefined,
    parent_id: row && row.id ? row.id : 0,
    name: '',
    icon: '',
    type: 0,
    sort: 0,
    permission: '',
    path: '',
    component: '',
    status: 1,
    hidden: 0,  // 默认显示
    redirect: ''
  }

  menuDialog.title = '添加菜单'
  menuDialog.visible = true
}

// 编辑菜单
const handleEdit = (row) => {
  // 深拷贝避免引用问题
  currentMenu.value = JSON.parse(JSON.stringify(row))
  menuDialog.title = '编辑菜单'
  menuDialog.visible = true
}

// 提交表单
const handleSubmitForm = (formData) => {
  menuDialog.loading = true

  // 根据是否有ID判断是新增还是编辑
  const apiRequest = formData.id ? editMenu(formData) : addMenu(formData)

  apiRequest.then(res => {
    if (res.code === 200) {
      menuDialog.loading = false
      menuDialog.visible = false
      ElMessage.success(formData.id ? '修改成功' : '新增成功')
      handleQuery()
    } else {
      menuDialog.loading = false
      ElMessage.error(res.message || (formData.id ? '修改失败' : '新增失败'))
    }
  }).catch(err => {
    console.error(formData.id ? '修改菜单失败:' : '新增菜单失败:', err)
    menuDialog.loading = false
    ElMessage.error(formData.id ? '修改失败' : '新增失败')
  })
}

// 删除菜单
const handleDelete = (row) => {
  ElMessageBox.confirm(`是否确认删除菜单名称为"${row.name}"的数据项?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loading.value = true
    deleteMenu(row.id).then(res => {
      if (res.code === 200) {
        ElMessage.success('删除成功')
        handleQuery()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
      loading.value = false
    }).catch(err => {
      console.error('删除菜单失败:', err)
      loading.value = false
      ElMessage.error('删除失败')
    })
  }).catch(() => {

  })
}

// 切换菜单状态
const handleStatusChange = (row) => {
  // 当前状态
  const currentStatus = row.status === 1 ? '启用' : '禁用'
  // 目标状态（用户要切换到的状态）
  const targetStatus = row.status === 1 ? '禁用' : '启用'
  // 目标状态的值
  const newStatus = row.status === 1 ? 0 : 1

  ElMessageBox.confirm(`确认要将${row.type === 0 ? '菜单' : '按钮'}"${row.name}"${targetStatus}吗?`, '操作确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loading.value = true
    // 调用状态变更接口
    changeStatus(row.id).then(res => {
      if (res.code === 200) {
        // 切换状态成功后，手动设置新状态
        row.status = newStatus
        ElMessage.success(`已成功${targetStatus}${row.type === 0 ? '菜单' : '按钮'}"${row.name}"`)
      } else {
        // 操作失败，状态不变
        ElMessage.error(res.message || `${targetStatus}失败`)
      }
      loading.value = false
    }).catch(err => {
      console.error('修改状态失败:', err)
      loading.value = false
      ElMessage.error(`${targetStatus}失败`)
    })
  }).catch(() => {
    // 用户取消，状态不变
  })
}

// 是否为目录/菜单（可以添加子菜单）
const isDirectory = (type) => {
  return type === 0  // 只有菜单类型可以添加子菜单
}

// 判断是否可以添加子菜单
const canAddChild = (row) => {
  // 只有菜单类型且非隐藏的菜单才能添加子菜单
  return row.type === 0 && !row.hidden
}

// 初始化菜单选项树
const initMenuOptions = () => {
  // 过滤菜单数据，排除按钮类型和隐藏菜单
  const filterMenus = (menus) => {
    if (!menus) return []

    return menus.map(item => {
      const newItem = { ...item }
      // 递归处理子菜单，过滤掉按钮类型和隐藏菜单
      if (newItem.children && newItem.children.length > 0) {
        newItem.children = filterMenus(newItem.children).filter(child => child.type === 0 && !child.hidden)
      }
      return newItem
    }).filter(item => item.type === 0 && !item.hidden) // 仅保留菜单类型且非隐藏的菜单
  }

  menuOptions.value = [
    { id: 0, name: '主类目', children: filterMenus(JSON.parse(JSON.stringify(originalMenuList.value))) }
  ]
}

// 初始化展开所有行
const initExpandAll = () => {
  // 获取所有顶级菜单的ID
  expandRowKeys.value = originalMenuList.value.map(item => item.id)
}

// 表格默认展开设置
const defaultExpandAll = ref(true)

// 获取菜单类型标签
const getMenuTypeTag = (type) => {
  if (type === 0) {
    return 'primary'
  } else if (type === 1) {
    return 'success'
  }
  return ''
}

// 获取菜单类型文本
const getMenuTypeText = (type) => {
  if (type === 0) {
    return '菜单'
  } else if (type === 1) {
    return '按钮'
  }
  return ''
}

// 渲染图标
const getIconComponent = (iconName) => {
  if (!iconName) return null
  if (ElementPlusIconsVue[iconName]) {
    return ElementPlusIconsVue[iconName]
  }
  return Document // 默认图标
}

// 处理API返回的菜单数据
const handleMenuData = (menuData) => {
  if (!menuData || !Array.isArray(menuData)) return []

  return menuData.map(item => {
    // 确保图标可用
    if (item.icon && !ElementPlusIconsVue[item.icon]) {
      console.warn(`图标 ${item.icon} 在Element Plus中不存在，将使用默认图标`)
    }

    if (item.children && item.children.length > 0) {
      item.children = handleMenuData(item.children)
    }
    return item
  })
}

// 搜索
const handleQuery = () => {
  loading.value = true

  // 调用API获取菜单树
  getMenuTree().then(res => {
    if (res.code === 200) {
      // 处理菜单数据，确保图标正确
      originalMenuList.value = handleMenuData(res.data)
      // 初始化展开的行
      initExpandAll()
      // 初始化菜单选项树
      initMenuOptions()
    } else {
      ElMessage.error(res.message || '获取菜单列表失败')
    }
    loading.value = false
  }).catch(err => {
    console.error('获取菜单列表失败:', err)
    loading.value = false
    ElMessage.error('获取菜单列表失败')
  })
}

// 重置搜索
const resetQuery = () => {
  queryParams.searchType = 'name'
  queryParams.searchValue = ''
  queryParams.status = ''
  queryParams.dateType = 'created_at'
  queryParams.dateRange = []
}

// 获取菜单状态标签类型
const getStatusTagType = (status) => {
  return status === 1 ? 'success' : 'danger'
}

// 获取菜单状态文本
const getStatusText = (status) => {
  return status === 1 ? '启用' : '禁用'
}

// 初始化
onMounted(() => {
  try {
    // 加载数据
    handleQuery()
  } catch (error) {
    console.error('菜单初始化失败:', error)
  }
})
</script>

<template>
  <div class="menu-container">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item prop="searchValue" class="search-item">
          <el-input v-model="queryParams.searchValue" placeholder="请输入搜索内容" clearable style="width: 240px"
            class="input-with-select">
            <template #prepend>
              <el-select v-model="queryParams.searchType" style="width: 110px">
                <el-option v-for="option in searchTypeOptions" :key="option.value" :label="option.label"
                  :value="option.value" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="status" class="search-item">
          <el-select v-model="queryParams.status" placeholder="菜单状态" clearable style="width: 120px">
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value">
              <template #default>
                <span style="display: flex; align-items: center;">
                  <el-tag :type="dict.value === 1 ? 'success' : 'danger'" size="small" style="margin-right: 8px;">
                    {{ dict.label }}
                  </el-tag>
                  {{ dict.value === 1 ? '可用状态' : '禁用状态' }}
                </span>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 
        <el-form-item prop="dateRange" class="search-item">
          <DateTypeRangePicker v-model:dateType="queryParams.dateType" v-model:dateRange="queryParams.dateRange"
            :dateTypeOptions="dateTypeOptions" value-format="YYYY-MM-DD" />
        </el-form-item>
         -->
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
          <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>菜单列表</span>
          <div class="right-buttons">
            <el-button type="primary" :icon="Plus" @click="handleAdd()">新增</el-button>
          </div>
        </div>
      </template>

      <!-- 表格 -->
      <el-table v-loading="loading" :data="menuList" row-key="id" :default-expand-all="defaultExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
        <el-table-column label="菜单名称" prop="name" :show-overflow-tooltip="true">
          <template #default="scope">
            <span v-if="scope.row.type === 0">
              <el-icon>
                <Folder />
              </el-icon>
            </span>
            <span v-else>
              <el-icon>
                <Operation />
              </el-icon>
            </span>
            {{ scope.row.name }}
            <el-tag v-if="scope.row.type === 0 && scope.row.hidden === 1" type="info" size="small"
              style="margin-left: 8px;">
              隐藏页面
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" width="80">
          <template #default="scope">
            <el-tag :type="getMenuTypeTag(scope.row.type)">
              {{ getMenuTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="80" align="center" />
        <el-table-column label="图标" align="center" width="80">
          <template #default="scope">
            <span v-if="scope.row.icon">
              <el-icon>
                <component :is="getIconComponent(scope.row.icon)" />
              </el-icon>
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="权限标识" prop="permission" :show-overflow-tooltip="true" />
        <el-table-column label="路由地址" prop="path" :show-overflow-tooltip="true" />
        <el-table-column label="组件路径" prop="component" :show-overflow-tooltip="true" />
        <el-table-column label="显示状态" align="center" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.type === 0" :type="scope.row.hidden ? 'info' : 'success'" size="small">
              {{ scope.row.hidden ? '隐藏' : '显示' }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-switch :model-value="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" active-text="启用" inactive-text="禁用" inline-prompt
              class="status-switch" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button type="primary" link :icon="Plus" v-if="canAddChild(scope.row)"
              @click="handleAdd(scope.row)">新增</el-button>
            <el-button type="primary" link :icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 菜单表单对话框 -->
    <MenuForm ref="menuFormRef" v-model:visible="menuDialog.visible" :title="menuDialog.title"
      :loading="menuDialog.loading" :form-data="currentMenu" :menu-options="menuOptions"
      :menu-type-options="menuTypeOptions" :status-options="statusOptions" @submit="handleSubmitForm" />
  </div>
</template>

<style scoped>
.menu-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.table-card {
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 500;
}

.right-buttons {
  display: flex;
  gap: 10px;
}

.status-switch :deep(.el-switch__core) {
  width: 55px !important;
}

/* 自定义搜索表单样式 */
.search-item {
  margin-right: 10px;
}

.input-with-select .el-input-group__prepend {
  background-color: var(--el-fill-color-blank);
  padding: 0;
}
</style>