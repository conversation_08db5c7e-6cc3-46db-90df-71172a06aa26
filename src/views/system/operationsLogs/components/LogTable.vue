<script setup>
import { Download, View } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { ElDialog, ElDescriptions, ElDescriptionsItem, ElTag } from 'element-plus'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    logList: {
        type: Array,
        required: true
    },
    total: {
        type: Number,
        default: 0
    },
    currentPage: {
        type: Number,
        default: 1
    },
    pageSize: {
        type: Number,
        default: 10
    }
})

const emit = defineEmits(['export', 'pagination-change'])

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
    emit('pagination-change', { page: props.currentPage, size })
}

const handleCurrentChange = (page) => {
    emit('pagination-change', { page, size: props.pageSize })
}

// 操作事件处理
const handleExport = () => {
    emit('export')
}

// 查看详情对话框
const detailDialog = ref(false)
const currentLogDetail = ref({})

const handleViewDetail = (row) => {
    currentLogDetail.value = row
    detailDialog.value = true
}

// 格式化HTTP方法标签类型
const getMethodTagType = (method) => {
    const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
    }
    return typeMap[method] || 'default'
}

// 格式化日期
const formatDate = (dateStr) => {
    if (!dateStr) return '-'
    return new Date(dateStr).toLocaleString('zh-CN')
}

// 格式化JSON数据
const formatJson = (data) => {
    if (!data || (Array.isArray(data) && data.length === 0)) return '无数据'
    try {
        return JSON.stringify(data, null, 2)
    } catch {
        return String(data)
    }
}
</script>

<template>
    <el-card class="table-card" shadow="hover">
        <template #header>
            <div class="card-header">
                <span>操作日志列表</span>
                <div class="right-buttons">
                    <el-button type="success" :icon="Download" @click="handleExport">导出</el-button>
                </div>
            </div>
        </template>

        <div v-if="logList.length === 0" class="empty-data">
            <p>暂无数据</p>
        </div>

        <!-- 表格 -->
        <el-table v-loading="loading" :data="logList">
            <el-table-column label="日志编号" prop="id" width="80" align="center" />
            <el-table-column label="用户" prop="user.name" width="120" :show-overflow-tooltip="true">
                <template #default="scope">
                    <span v-if="scope.row.user">{{ scope.row.user.name }}</span>
                    <span v-else class="text-gray-400">未知用户</span>
                </template>
            </el-table-column>
            <el-table-column label="路由" prop="route" :show-overflow-tooltip="true" />
            <el-table-column label="请求方法" prop="method" width="100" align="center">
                <template #default="scope">
                    <el-tag :type="getMethodTagType(scope.row.method)" size="small">
                        {{ scope.row.method }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="IP地址" prop="ip_address" width="140" :show-overflow-tooltip="true" />
            <el-table-column label="创建时间" prop="created_at" width="180">
                <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
                <template #default="scope">
                    <div class="action-btns">
                        <el-button link type="primary" @click="() => handleViewDetail(scope.row)">
                            <el-icon>
                                <View />
                            </el-icon>详情
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100]"
                :current-page="currentPage" :page-size="pageSize" :total="total" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialog" title="操作日志详情" width="60%" top="5vh">
        <el-descriptions border :column="2">
            <el-descriptions-item label="日志编号">{{ currentLogDetail.id }}</el-descriptions-item>
            <el-descriptions-item label="用户">
                {{ currentLogDetail.user?.name || '未知用户' }}
            </el-descriptions-item>
            <el-descriptions-item label="用户邮箱">
                {{ currentLogDetail.user?.email || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="路由">{{ currentLogDetail.route }}</el-descriptions-item>
            <el-descriptions-item label="请求方法">
                <el-tag :type="getMethodTagType(currentLogDetail.method)" size="small">
                    {{ currentLogDetail.method }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="IP地址">{{ currentLogDetail.ip_address }}</el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
                {{ formatDate(currentLogDetail.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间" :span="2">
                {{ formatDate(currentLogDetail.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
                {{ currentLogDetail.remark || '-' }}
            </el-descriptions-item>
        </el-descriptions>

        <div class="mt-4">
            <h4>用户代理</h4>
            <div class="bg-gray-50 p-3 rounded text-sm break-words">
                {{ currentLogDetail.user_agent || '-' }}
            </div>
        </div>

        <div class="mt-4">
            <h4>请求数据</h4>
            <div class="bg-gray-50 p-3 rounded">
                <pre class="text-sm whitespace-pre-wrap">{{ formatJson(currentLogDetail.request_data) }}</pre>
            </div>
        </div>

        <div class="mt-4">
            <h4>响应数据</h4>
            <div class="bg-gray-50 p-3 rounded">
                <pre class="text-sm whitespace-pre-wrap">{{ formatJson(currentLogDetail.response_data) }}</pre>
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="detailDialog = false">关闭</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style scoped>
.table-card {
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: none;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 500;
}

.right-buttons {
    display: flex;
    gap: 10px;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.action-btns {
    display: flex;
    justify-content: center;
}

.empty-data {
    text-align: center;
    padding: 40px 0;
    color: #909399;
}

/* 详情对话框样式 */
h4 {
    margin: 16px 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
}

pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
}

.text-gray-400 {
    color: #9ca3af;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.p-3 {
    padding: 12px;
}

.rounded {
    border-radius: 6px;
}

.text-sm {
    font-size: 12px;
}

.break-words {
    word-break: break-words;
}

.whitespace-pre-wrap {
    white-space: pre-wrap;
}

.mt-4 {
    margin-top: 16px;
}
</style>