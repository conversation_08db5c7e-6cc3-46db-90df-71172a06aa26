<script setup>
import { Search, Refresh } from '@element-plus/icons-vue'
// import DateTypeRangePicker from '@/components/DateTypeRangePicker.vue'

defineProps({
    queryParams: {
        type: Object,
        required: true
    },
    searchTypeOptions: {
        type: Array,
        required: true
    },
    dateTypeOptions: {
        type: Array,
        required: true
    }
})

const emit = defineEmits(['search', 'reset'])

const handleSearch = () => {
    emit('search')
}

const handleReset = () => {
    emit('reset')
}
</script>

<template>
    <el-card class="search-card" shadow="hover">
        <el-form :model="queryParams" inline>
            <el-form-item prop="searchValue" class="search-item">
                <el-input v-model="queryParams.searchValue" placeholder="请输入搜索内容" clearable style="width: 240px"
                    class="input-with-select">
                    <template #prepend>
                        <el-select v-model="queryParams.searchType" style="width: 110px">
                            <el-option v-for="option in searchTypeOptions" :key="option.value" :label="option.label"
                                :value="option.value" />
                        </el-select>
                    </template>
                </el-input>
            </el-form-item>
            <!-- <el-form-item prop="dateRange" class="search-item">
                <DateTypeRangePicker v-model:dateType="queryParams.dateType" v-model:dateRange="queryParams.dateRange"
                    :dateTypeOptions="dateTypeOptions" value-format="YYYY-MM-DD" />
            </el-form-item> -->
            <el-form-item>
                <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
                <el-button :icon="Refresh" @click="handleReset">重置</el-button>
            </el-form-item>
        </el-form>
    </el-card>
</template>

<style scoped>
.search-card {
    margin-bottom: 20px;
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: none;
}

.search-item {
    margin-right: 10px;
}

.input-with-select .el-input-group__prepend {
    background-color: var(--el-fill-color-blank);
    padding: 0;
}
</style>