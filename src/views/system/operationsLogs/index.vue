<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getOperationLogList } from '@/api/operationLog'
import LogSearch from './components/LogSearch.vue'
import LogTable from './components/LogTable.vue'

// 查询条件
const queryParams = reactive({
    searchType: 'route', // 默认搜索类型为路由
    searchValue: '', // 搜索值
    dateType: 'created_at', // 默认日期类型为创建时间
    dateRange: []
})

// 搜索类型选项 - 去除IP地址搜索
const searchTypeOptions = [
    { value: 'route', label: '路由' },
    { value: 'user_id', label: '用户ID' }
]

// 日期类型选项
const dateTypeOptions = [
    { value: 'created_at', label: '创建时间' }
]

// 表格数据
const logList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(15)

// 表格加载状态
const loading = ref(false)

// 搜索
const handleQuery = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const params = {
            page: currentPage.value,
            limit: pageSize.value
        }

        // 根据搜索类型和值拼接参数
        if (queryParams.searchValue) {
            params[queryParams.searchType] = queryParams.searchValue
        }

        // 处理日期范围
        if (queryParams.dateRange && queryParams.dateRange.length === 2) {
            const [startDate, endDate] = queryParams.dateRange
            params.start_date = startDate + ' 00:00:00'
            params.end_date = endDate + ' 23:59:59'
        }

        const response = await getOperationLogList(params)

        if (response && response.data) {
            logList.value = response.data.data || []
            total.value = response.data.total || 0
            currentPage.value = response.data.current_page || 1
            pageSize.value = response.data.limit || 15
        }
    } catch (error) {
        console.error('获取操作日志列表失败', error)
        ElMessage.error('获取操作日志列表失败')
    } finally {
        loading.value = false
    }
}

// 重置搜索
const resetQuery = () => {
    queryParams.searchType = 'route'
    queryParams.searchValue = ''
    queryParams.dateType = 'created_at'
    queryParams.dateRange = []
    currentPage.value = 1
    pageSize.value = 15
    handleQuery()
}

// 处理分页变化
const handlePaginationChange = ({ page, size }) => {
    currentPage.value = page
    pageSize.value = size
    handleQuery()
}

// 导出操作日志
const handleExport = () => {
    ElMessage.success('导出成功')
}

// 初始化
onMounted(() => {
    handleQuery()
})
</script>

<template>
    <div class="logs-container">
        <!-- 搜索区域 -->
        <LogSearch :queryParams="queryParams" :searchTypeOptions="searchTypeOptions" :dateTypeOptions="dateTypeOptions"
            @search="handleQuery" @reset="resetQuery" />

        <!-- 操作日志表格 -->
        <LogTable :loading="loading" :logList="logList" :total="total" :currentPage="currentPage" :pageSize="pageSize"
            @export="handleExport" @pagination-change="handlePaginationChange" />
    </div>
</template>

<style scoped>
.logs-container {
    padding: 20px;
}
</style>