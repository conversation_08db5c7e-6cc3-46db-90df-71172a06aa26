<script setup>
// 1. 导入 (Imports)
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList, addUser, updateUser, deleteUser, batchDeleteUser, resetPassword } from '@/api/user'
import UserSearch from './components/UserSearch.vue'
import UserTable from './components/UserTable.vue'
import UserForm from './components/UserForm.vue'
import ResetPasswordDialog from './components/ResetPasswordDialog.vue'

// 2. Props & Emits 定义 (此组件无props和emits)

// 3. 响应式状态 (Reactive State)
// 查询条件
const queryParams = reactive({
  searchType: 'name', // 默认搜索类型为用户名称
  searchValue: '', // 搜索值
  status: '',
  dateType: 'createTime', // 默认日期类型为创建时间
  dateRange: []
})

// 搜索类型选项
const searchTypeOptions = [
  { value: 'name', label: '用户名称' },
  { value: 'email', label: '邮箱' }
]

// 日期类型选项
const dateTypeOptions = [
  { value: 'createTime', label: '创建时间' },
  { value: 'updateTime', label: '更新时间' }
]

// 状态字典
const statusOptions = [
  { value: '1', label: '正常' },
  { value: '0', label: '停用' }
]

// 表格数据
const userList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(15)

// 表格加载状态
const loading = ref(false)

// 表格选中行
const selectedRows = ref([])

// 用户表单对话框
const userDialog = reactive({
  visible: false,
  title: '',
  loading: false
})

// 用户表单
const userForm = reactive({
  id: undefined,
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  roles: []
})

// 重置密码对话框
const resetPasswordDialog = reactive({
  visible: false,
  loading: false,
  user: null
})

// 4. 计算属性 (Computed Properties) - 此组件暂无计算属性

// 5. 方法 (Methods)
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 搜索
const handleQuery = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    }

    // 根据搜索类型和值拼接参数
    if (queryParams.searchValue) {
      params[queryParams.searchType] = queryParams.searchValue
    }

    // 处理状态筛选
    if (queryParams.status) {
      params.status = queryParams.status
    }

    // 处理日期范围
    if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      const [startDate, endDate] = queryParams.dateRange
      params.start_time = startDate
      params.end_time = endDate
      params.date_type = queryParams.dateType
    }

    const response = await getUserList(params)

    if (response && response.data) {
      userList.value = response.data.data || []
      total.value = response.data.total || 0
      currentPage.value = response.data.current_page || 1
      pageSize.value = response.data.limit || 15
    }
  } catch (error) {
    console.error('获取用户列表失败', error)
    const errorMessage = error?.response?.data?.message || error?.message || '获取用户列表失败'
    ElMessage.error(`获取用户列表失败: ${errorMessage}`)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetQuery = () => {
  queryParams.searchType = 'name'
  queryParams.searchValue = ''
  queryParams.status = ''
  queryParams.dateType = 'createTime'
  queryParams.dateRange = []
  currentPage.value = 1
  pageSize.value = 15
  handleQuery()
}

// 处理分页变化
const handlePaginationChange = ({ page, size }) => {
  currentPage.value = page
  pageSize.value = size
  handleQuery()
}

// 新增用户
const handleAdd = () => {
  userDialog.visible = true
  userDialog.title = '添加用户'
  resetForm()
}

// 编辑用户
const handleEdit = (row) => {
  userDialog.visible = true
  userDialog.title = '编辑用户'
  resetForm()

  // 填充表单数据
  userForm.id = row.id
  userForm.name = row.name
  userForm.email = row.email
  // 填充用户角色
  userForm.roles = row.roles ? row.roles.map(role => role.name) : []
}

// 提交表单
const submitForm = async () => {
  userDialog.loading = true

  try {
    if (userForm.id) {
      // 编辑用户
      await updateUser(userForm)
      ElMessage.success('修改成功')
    } else {
      // 新增用户
      await addUser(userForm)
      ElMessage.success('新增成功')
    }
    userDialog.visible = false
    handleQuery()
  } catch (error) {
    console.error('提交表单失败', error)
    const errorMessage = error?.response?.data?.message || error?.message || '操作失败'
    ElMessage.error(`操作失败: ${errorMessage}`)
  } finally {
    userDialog.loading = false
  }
}

// 重置表单
const resetForm = () => {
  userForm.id = undefined
  userForm.name = ''
  userForm.email = ''
  userForm.password = ''
  userForm.confirmPassword = ''
  userForm.roles = []
}

// 删除用户
const handleDelete = async (row) => {
  const userId = row.id

  try {
    await ElMessageBox.confirm(`确认删除该用户吗?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true
    await deleteUser({ id: userId })
    ElMessage.success('删除成功')
    handleQuery()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败', error)
      const errorMessage = error?.response?.data?.message || error?.message || '删除失败'
      ElMessage.error(`删除失败: ${errorMessage}`)
    }
  } finally {
    loading.value = false
  }
}

// 批量删除用户
const handleBatchDelete = async (rows) => {
  const userIds = rows.map(item => item.id)

  try {
    await ElMessageBox.confirm(`确认删除选中的${userIds.length}条数据?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true
    await batchDeleteUser({ ids: userIds })
    ElMessage.success('批量删除成功')
    handleQuery()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除用户失败', error)
      const errorMessage = error?.response?.data?.message || error?.message || '批量删除失败'
      ElMessage.error(`批量删除失败: ${errorMessage}`)
    }
  } finally {
    loading.value = false
  }
}

// 导出用户
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 打开重置密码对话框
const handleResetPassword = (row) => {
  console.log('Opening reset password dialog for user:', row)
  resetPasswordDialog.user = row
  resetPasswordDialog.visible = true
}

// 确认重置密码
const confirmResetPassword = async (passwordData) => {
  if (!resetPasswordDialog.user) return

  resetPasswordDialog.loading = true
  try {
    await resetPassword({
      id: resetPasswordDialog.user.id,
      password: passwordData.password,
      confirm_password: passwordData.confirm_password
    })
    ElMessage.success('密码重置成功')
    resetPasswordDialog.visible = false
  } catch (error) {
    console.error('重置密码失败', error)
    ElMessage.error('重置密码失败')
  } finally {
    resetPasswordDialog.loading = false
  }
}

// 初始化
onMounted(() => {
  handleQuery()
})
</script>

<template>
  <div class="user-container">
    <!-- 搜索区域 -->
    <UserSearch :queryParams="queryParams" :searchTypeOptions="searchTypeOptions" :statusOptions="statusOptions"
      :dateTypeOptions="dateTypeOptions" @search="handleQuery" @reset="resetQuery" />

    <!-- 用户表格 -->
    <UserTable :loading="loading" :userList="userList" :total="total" :currentPage="currentPage" :pageSize="pageSize"
      @add="handleAdd" @edit="handleEdit" @delete="handleDelete" @batch-delete="handleBatchDelete"
      @export="handleExport" @pagination-change="handlePaginationChange" @selection-change="handleSelectionChange"
      @reset-password="handleResetPassword" />

    <!-- 用户表单对话框 -->
    <UserForm :visible="userDialog.visible" @update:visible="(val) => userDialog.visible = val"
      :title="userDialog.title" :loading="userDialog.loading" :userForm="userForm" @submit="submitForm" />

    <!-- 重置密码对话框 -->
    <ResetPasswordDialog :visible="resetPasswordDialog.visible"
      @update:visible="(val) => resetPasswordDialog.visible = val" :loading="resetPasswordDialog.loading"
      :username="resetPasswordDialog.user?.name || ''" @confirm="confirmResetPassword" />
  </div>
</template>

<style scoped>
.user-container {
  padding: 20px;
}
</style>