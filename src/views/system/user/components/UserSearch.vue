<script setup>
import { Search, Refresh } from '@element-plus/icons-vue'
import { defineProps, defineEmits } from 'vue'

defineProps({
  queryParams: {
    type: Object,
    required: true,
  },
  searchTypeOptions: {
    type: Array,
    required: true,
  },
  statusOptions: {
    type: Array,
    required: true,
  },
  dateTypeOptions: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['search', 'reset'])

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}
</script>

<template>
  <el-card class="search-card" shadow="hover">
    <el-form :model="queryParams" inline>
      <el-form-item prop="searchValue" class="search-item">
        <el-input
          v-model="queryParams.searchValue"
          placeholder="请输入搜索内容"
          clearable
          style="width: 240px"
          class="input-with-select"
        >
          <template #prepend>
            <el-select v-model="queryParams.searchType" style="width: 100px">
              <el-option
                v-for="option in searchTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="status" class="search-item">
        <el-select
          v-model="queryParams.status"
          placeholder="用户状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <template #default>
              <span style="display: flex; align-items: center">
                <el-tag
                  :type="dict.value === '1' ? 'success' : 'danger'"
                  size="small"
                  style="margin-right: 8px"
                >
                  {{ dict.label }}
                </el-tag>
                {{ dict.value === '1' ? '开启状态' : '禁用状态' }}
              </span>
            </template>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="dateRange" class="search-item">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
        <el-button :icon="Refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<style scoped>
.search-card {
  margin-bottom: 20px;
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow); 
  border: none;
}
</style>
