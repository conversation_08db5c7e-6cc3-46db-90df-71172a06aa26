<script setup>
import { ref, defineProps, defineEmits, reactive } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  username: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 密码表单
const passwordForm = reactive({
  password: '',
  confirmPassword: ''
})

// 表单引用
const formRef = ref(null)

// 表单校验规则
const rules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: '密码必须包含至少一个大写字母、一个小写字母和一个数字',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const closeDialog = () => {
  resetForm()
  emit('update:visible', false)
}

// 重置表单
const resetForm = () => {
  passwordForm.password = ''
  passwordForm.confirmPassword = ''
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 确认重置密码
const confirmReset = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('confirm', {
      password: passwordForm.password,
      confirm_password: passwordForm.confirmPassword
    })
  } catch (error) {
    console.error('表单校验失败', error)
  }
}
</script>

<template>
  <el-dialog
    title="重置密码"
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    width="400px"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="reset-password-content">
      <el-alert
        title="请为用户设置新密码"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;"
      />
      
      <el-form 
        ref="formRef" 
        :model="passwordForm" 
        :rules="rules" 
        label-width="100px"
      >
        <el-form-item label="用户名">
          <el-input :value="username" disabled />
        </el-form-item>
        
        <el-form-item label="新密码" prop="password">
          <el-input 
            v-model="passwordForm.password" 
            type="password" 
            placeholder="请输入新密码" 
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码" 
            show-password
          />
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          type="primary"
          @click="confirmReset"
          :loading="loading"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.reset-password-content {
  padding: 10px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 