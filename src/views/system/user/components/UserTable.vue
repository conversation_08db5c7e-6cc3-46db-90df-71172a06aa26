<script setup>
import { Plus, Edit, Delete, Download, Key } from '@element-plus/icons-vue'
import { ref, defineProps, defineEmits } from 'vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  userList: {
    type: Array,
    required: true
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['add', 'edit', 'delete', 'batch-delete', 'export', 'pagination-change', 'selection-change', 'reset-password'])

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
  emit('pagination-change', { page: props.currentPage, size })
}

const handleCurrentChange = (page) => {
  emit('pagination-change', { page, size: props.pageSize })
}

// 操作事件处理
const handleAdd = () => {
  emit('add')
}

const handleEdit = (row) => {
  emit('edit', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}

const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    return
  }
  emit('batch-delete', selectedRows.value)
}

const handleExport = () => {
  emit('export')
}

// 重置密码
const handleResetPassword = (row) => {
  emit('reset-password', row)
}
</script>

<template>
  <el-card class="table-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span>用户列表</span>
        <div class="right-buttons">
          <el-button type="primary" :icon="Plus" @click="handleAdd" v-permission="['users.create']">新增</el-button>
          <el-button type="danger" :icon="Delete" @click="handleBatchDelete" :disabled="selectedRows.length === 0"
            v-permission="['users.delete']">
            删除
          </el-button>
          <el-button type="success" :icon="Download" @click="handleExport"
            v-permission="['users.export']">导出</el-button>
        </div>
      </div>
    </template>

    <div v-if="userList.length === 0" class="empty-data">
      <p>暂无数据</p>
    </div>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="用户编号" prop="id" width="80" align="center" />
      <el-table-column label="用户名称" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" prop="created_at" width="180" />
      <el-table-column label="操作" align="center" width="220">
        <template #default="scope">
          <div class="action-btns">
            <el-button link type="primary" @click="() => handleEdit(scope.row)"
              v-permission="['users.edit']">
              <el-icon>
                <Edit />
              </el-icon>编辑
            </el-button>

            <el-button link type="warning" @click="() => handleResetPassword(scope.row)"
              v-permission="['users.reset_password']">
              <el-icon>
                <Key />
              </el-icon>重置密码
            </el-button>

            <el-button link type="danger" @click="() => handleDelete(scope.row)"
              v-permission="['users.delete']">
              <el-icon>
                <Delete />
              </el-icon>删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100]"
        :current-page="currentPage" :page-size="pageSize" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </el-card>
</template>

<style scoped>
.table-card {
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 500;
}

.right-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.action-btns {
  display: flex;
  justify-content: center;
}


</style>