<script setup>
// 1. 导入 (Imports)
import { ref, defineProps, defineEmits, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getRoleList } from '@/api/role'

// 2. Props & Emits 定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  userForm: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 3. 响应式状态 (Reactive State)
const userFormRef = ref(null)

// 角色列表
const roleList = ref([])
const roleLoading = ref(false)

// 表单校验规则
const userFormRules = {
  name: [
    { required: true, message: '用户名不能为空', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
      message: '用户名只能包含字母、数字、下划线和中文',
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '邮箱不能为空', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱格式',
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
      message: '密码必须包含至少一个大写字母、一个小写字母和一个数字',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '确认密码不能为空', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== props.userForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  roles: [
    { required: true, message: '请至少选择一个角色', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (!Array.isArray(value) || value.length === 0) {
          callback(new Error('请至少选择一个角色'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 4. 计算属性 (Computed Properties) - 此组件暂无计算属性

// 5. 方法 (Methods)
// 获取角色列表
const fetchRoleList = async () => {
  roleLoading.value = true
  try {
    const response = await getRoleList()
    if (response && response.data) {
      roleList.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取角色列表失败', error)
    const errorMessage = error?.response?.data?.message || error?.message || '获取角色列表失败'
    ElMessage.error(`获取角色列表失败: ${errorMessage}`)
  } finally {
    roleLoading.value = false
  }
}

const closeDialog = () => {
  emit('update:visible', false)
}

const submitForm = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()
    emit('submit')
  } catch (error) {
    console.error('表单校验失败', error)
    ElMessage.error('表单校验失败，请检查输入内容')
  }
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchRoleList()
  }
})

// 6. 生命周期钩子 (Lifecycle Hooks)
onMounted(() => {
  if (props.visible) {
    fetchRoleList()
  }
})
</script>

<template>
  <el-dialog :title="title" :model-value="visible" @update:model-value="(val) => emit('update:visible', val)"
    width="500px" append-to-body destroy-on-close :close-on-click-modal="false">
    <el-form ref="userFormRef" :model="userForm" :rules="userFormRules" label-width="100px">
      <el-form-item label="用户名称" prop="name">
        <el-input v-model="userForm.name" placeholder="请输入用户名称" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="userForm.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item label="密码" prop="password" v-if="!userForm.id">
        <el-input v-model="userForm.password" type="password" placeholder="请输入密码" />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword" v-if="!userForm.id">
        <el-input v-model="userForm.confirmPassword" type="password" placeholder="请确认密码" />
      </el-form-item>
      <el-form-item label="用户角色" prop="roles">
        <el-select v-model="userForm.roles" multiple placeholder="请选择用户角色" style="width: 100%" :loading="roleLoading">
          <el-option v-for="role in roleList" :key="role.id" :label="role.role_name || role.name" :value="role.name">
            <div class="role-option">
              <span class="role-name">{{ role.role_name || role.name }}</span>
              <el-tag :type="role.status === 1 ? 'success' : 'danger'" size="small" class="role-tag">
                {{ role.status === 1 ? '正常' : '停用' }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.role-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.role-name {
  flex: 1;
  font-weight: 500;
}

.role-tag {
  margin-left: 8px;
  font-size: 12px;
}
</style>