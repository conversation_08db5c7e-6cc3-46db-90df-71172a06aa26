<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Setting } from '@element-plus/icons-vue'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    barcodeDisabled: {
        type: Boolean,
        default: false
    },
    moreBarcodeDisabled: {
      type: Boolean,
      default: false
    },
    moreNumDisabled: {
      type: Boolean,
      default: true
    },
    moreBtnFinDisabled: {
      type: Boolean,
      default: true
    },
    activeName: {
      type: String,
      default: ''
    },
})

const emit = defineEmits(['scan', 'more-scan', 'setting', 'tab-click'])


//一单一件
const orderNo = ref('')
const barcode = ref('')

//一单多件
const batchReview = ref(false)
const moreOrderNo = ref('')
const moreBarcode = ref('')
const moreNum = ref(1)


// SCAN
const handleScan = () => {
  emit('scan', orderNo.value)

  orderNo.value = ''
}

// SCAN
const handleMoreScan = () => {
  emit('more-scan', moreOrderNo.value)

  moreOrderNo.value = ''
}


//SETTING
const showSetting = () => {
    emit('setting')
}

const handleTabClick = (tab) => {
  emit('tab-click', tab.paneName)
}

const handleWeightReset = () => {
  moreOrderNo.value = ''
  moreBarcode.value = ''
}

const handleReviewFinish = () => {

}

</script>

<template>
    <el-card class="scan-card" shadow="hover">
        <template #header>
            <div class="card-header">
                <span style="font-size: 24px;">复核验货</span>
            </div>
        </template>

      <div class="scan-area">
        <el-tabs v-model="props.activeName" type="card" @tab-click="handleTabClick">
          <el-tab-pane label="一单一件" name="single">
            <div class="scan-area">
              <el-row style="font-weight: bold">
                波次单号
              </el-row>
              <el-row>
                <el-input
                    v-model="orderNo"
                    style="width: 280px; height: 40px; margin-top: 20px;"
                    placeholder="请扫描"
                    clearable
                    @keyup.enter="handleScan"
                />
              </el-row>

              <el-row style="font-weight: bold; margin-top: 20px;">
                Barcode
              </el-row>
              <el-row>
                <el-input
                    v-model="barcode"
                    :disabled="barcodeDisabled"
                    style="width: 280px; height: 40px; margin-top: 10px;"
                    placeholder="请扫描"
                    clearable
                    @keyup.enter=""
                />
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="一单多件" name="more">

            <div class="scan-area">
              <el-row>
                <el-checkbox label="支持批量复核(允许输入数量)" v-model="batchReview" />
              </el-row>

              <el-row style="margin-top: 20px;">
                出库单号/物流跟踪号/格子号
              </el-row>
              <el-row>
                <el-input
                    v-model="moreOrderNo"
                    style="width: 280px; height: 40px; margin-top: 10px;"
                    placeholder="请扫描"
                    clearable
                    @keyup.enter="handleMoreScan"
                />
              </el-row>

              <el-row style="font-weight: bold; margin-top: 20px;">
                Barcode
              </el-row>
              <el-row>
                <el-input
                    v-model="moreBarcode"
                    :disabled="moreBarcodeDisabled"
                    style="width: 280px; height: 40px; margin-top: 10px;"
                    placeholder="请扫描"
                    clearable
                    @keyup.enter=""
                />
              </el-row>

              <row  v-if="batchReview">
                <el-row style="font-weight: bold; margin-top: 20px;">
                  数量
                </el-row>
                <el-row>
                  <el-input
                      v-model="moreNum"
                      :disabled="moreNumDisabled"
                      style="width: 280px; height: 40px; margin-top: 10px;"
                      clearable
                      @keyup.enter=""
                  />
                </el-row>

                <el-row style="margin-top: 25px;">
                  <el-button type="primary" plain style="width: 200px; height: 40px;" :disabled="moreBtnFinDisabled"
                             @click="handleReviewFinish">
                    复核完成
                  </el-button>
                </el-row>
              </row>

              <el-row style="margin-top: 15px;">
                <el-button type="info" plain style="width: 200px; height: 40px;" @click="handleWeightReset">
                  重置
                </el-button>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div  class="footer">
          <el-select disabled="" placeholder="请选择打印机" style="width: 200px;">
            <el-option key="1" value="1">请选择打印机</el-option>
          </el-select>
          <el-link style="margin-left: 10px;">
            <el-icon @click="showSetting">
              <Setting />
            </el-icon>
          </el-link>
        </div>
      </div>

    </el-card>
</template>

<style scoped>
.scan-card {
    border-radius: 8px;
    height: calc(100vh - 120px);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: none;
}

.scan-area {
  position: relative;
  height: 70vh;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: 0px;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 500;
}


</style>