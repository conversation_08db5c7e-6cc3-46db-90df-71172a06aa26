<script setup>
import { defineProps } from 'vue'
import {useRouter} from "vue-router";
import { DocumentCopy } from '@element-plus/icons-vue'

const router = useRouter()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  orderList: {
    type: Array,
    default: () => [],
  },
  orderData: {
    type: Array,
    default: () => [],
  },
  orderMoreList: {
    type: Array,
    default: () => [],
  },
  orderListTips: {
    type: String,
    default: '',
  },
})


//复制
const copy = async (val) => {
  await navigator.clipboard.writeText(val);
  ElMessage.success('复制成功')
}

const handleDetail = (row) => {
  router.push({
    path: '/outbound/detail',
    query:  {deliveryNo: row.deliveryNo, customerCode:row.customerCode, whCode:row.whCode, sourceNo: row.sourceNo},
  })
}

</script>

<template>
  <div class="profile-container" v-if="orderList.length > 0" v-loading="loading">
    <el-form label-width="100px" label-position="left" >
      <el-card class="profile-card" shadow="hover" >
        <div class="app-container">
          <!--        <span style="font-size: 20px; font-weight: bold; color:#0071e3">丨</span>-->
          <span style="font-size: 22px; font-weight: bolder;" v-if="orderList.length > 0">
          {{ orderList[0].sourceNo }}
        </span>
        <span style="font-size: 22px; float:right;" v-if="orderList.length > 0">
          <label style="color: #9ca3af; font-size: 18px;">包裹数量</label>
          {{ orderList.length }}
        </span>
        </div>
        <div class="app-container">
          <el-table :data="orderList" style="width: 100%;margin-top: 20px;">
            <el-table-column label="序号" type="index" width="100"/>
            <el-table-column label="物流跟踪号" prop="expressNo" />
            <el-table-column label="承运商" prop="logisticsCarrier" align="center" />
            <el-table-column label="操作" prop="" width="180px;" align="center">
              <template #default="scope">
                <el-button link type="primary" @click="() => printOrder(scope.row)">
                  打印面单
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <el-card class="profile-card" shadow="hover" style="margin-top: 20px;">
        <div class="app-container">
          <span style="font-size: 22px; font-weight: bolder;" v-if="orderList.length > 0">
          本次已扫描
          </span>
        </div>
        <div class="app-container">
          <el-table :data="orderData" style="width: 100%;margin-top: 20px;">
            <el-table-column label="序号" type="index" prop="" width="70"/>
            <el-table-column label="物流跟踪号" prop="expressNo"  width="250px;" >
              <template #default="scope">
                <span v-if="scope.row.expressNo">
                  {{ scope.row.expressNo }}
                  <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                           :icon="DocumentCopy" @click="copy(scope.row.expressNo)">
                  </el-link>
                </span>
                  <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="出库单号" prop="" width="250px" align="center" >
              <template #default="scope">
                <span v-if="scope.row.sourceNo">
                  <el-link type="primary" v-if="scope.row.sourceNo"  @click="() => handleDetail(scope.row)">
                      {{ scope.row.sourceNo }}
                    </el-link>
                  <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                           :icon="DocumentCopy" @click="copy(scope.row.sourceNo)">
                  </el-link>
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="平台单号" prop="platformOrderNo" width="250px" align="center" />
            <el-table-column label="参考单号" prop="referOrderNo" width="250px" align="center" />
            <el-table-column label="操作" prop="" width="180px;" align="center" fixed="right">
              <template #default="scope">
                <el-button link type="primary" @click="() => printOrder(scope.row)">
                  打印面单
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-form>
  </div>

  <div class="defaultTips"  v-else>
    <img src="/scan.svg" />
    <label>
      {{ props.orderListTips }}
    </label>
  </div>
</template>

<style>
.defaultTips {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
  height: 100vh;
}
</style>
