<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Setting } from '@element-plus/icons-vue'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    weightDisabled: {
      type: Boolean,
      default: true
    },
    weightFinishDisabled: {
      type: Boolean,
      default: true
    }
})

const emit = defineEmits(['scan', 'setting', 'weight-finish'])
const orderNo = ref('')
const weight = ref('')

// SCAN
const handleScan = () => {
  emit('scan', orderNo.value)
}

//SETTING
const showSetting = () => {
    emit('setting')
}

const handleWeightFinish = () => {
  emit('weight-finish', orderNo.value, weight.value)
}

const handleWeightReset = () => {
  orderNo.value = ''
  weight.value = ''
}

</script>

<template>
    <el-card class="scan-card" shadow="hover">
        <template #header>
            <div class="card-header">
                <span style="font-size: 24px;">称重</span>
            </div>
        </template>

        <div class="scan-area">
          <el-row style="font-weight: bold">
            出库单号/物流跟踪号
          </el-row>
          <el-row>
              <el-input
                  v-model="orderNo"
                  style="width: 280px; height: 40px; margin-top: 10px;"
                  placeholder="请扫描"
                  clearable
                  @keyup.enter="handleScan"
              />
          </el-row>
          <el-row style="font-weight: bold; margin-top: 20px;">
            重量(kg)
          </el-row>
          <el-row>
            <el-input
                v-model="weight"
                :disabled="weightDisabled"
                style="width: 280px; height: 40px; margin-top: 10px;"
                placeholder="请输入"
                clearable
                @keyup.enter=""
            />
          </el-row>

          <el-row style="margin-top: 20px;">
            <el-button type="primary" :disabled="weightFinishDisabled"
                       @click="handleWeightFinish"
                       style="width: 180px; height: 40px;">
              称重完成
            </el-button>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-button type="info" plain style="width: 180px; height: 40px;" @click="handleWeightReset">
              重置
            </el-button>
          </el-row>

          <div  class="footer">
<!--            <el-select disabled="" placeholder="请选择打印机" style="width: 200px;">-->
<!--              <el-option key="1" value="1">请选择打印机</el-option>-->
<!--            </el-select>-->
            <el-link style="margin-left: 10px;">
              <el-icon @click="showSetting">
                <Setting />
              </el-icon>
            </el-link>
          </div>
        </div>

    </el-card>


</template>

<style scoped>
.scan-card {
    border-radius: 8px;
    height: calc(100vh - 120px);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: none;
}

.scan-area {
  position: relative;
  height: 70vh;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: 0px;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 500;
}


</style>