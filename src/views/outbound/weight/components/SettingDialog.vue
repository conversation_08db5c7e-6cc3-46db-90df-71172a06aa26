<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    settingData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['success', 'cancel'])


// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 确定
const submitForm = async () => {

    try {

        // 通知父组件操作成功
        emit('success', props.settingData)
    } catch (error) {
        console.error('操作失败', error)
    }
}

</script>

<template>
    <el-dialog :title="title"  :model-value="visible" width="400px">
      <br>
        <el-form label-width="0px" :model="settingData">
            <el-form-item label="" prop="">
              <el-checkbox label="测量包裹尺寸" v-model="settingData.toSize" />
            </el-form-item>

          <el-form-item label="" prop="">
            <el-checkbox label="扫描包材" v-model="settingData.scanPack" />
          </el-form-item>

          <el-form-item label="" prop="">
            <el-checkbox label="称重后立即出库" v-model="settingData.outOfStock" />
          </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="submitForm" >
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>