<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import { getWaveDetail } from '@/api/outbound/wave'
import { useWaveConfig, useWaveStatus } from '@/composables/useWaveConfig'
import WaveBasicInfo from './components/WaveBasicInfo.vue'
import WaveOrderList from './components/WaveOrderList.vue'
import WaveSkuDetail from './components/WaveSkuDetail.vue'
import WaveOperationLog from './components/WaveOperationLog.vue'

// 路由相关
const route = useRoute()

// 波次详情数据
const loading = ref(false)
const waveDetail = ref({})
const waveNo = ref(route.params.waveNo || route.query.waveNo)

// 标签页
const activeTab = ref('orders')

// 使用配置组合式函数
const { getVisibleDetailActions } = useWaveConfig()
const { getStatusTagType } = useWaveStatus()

// 获取波次详情
const fetchWaveDetail = async () => {
  if (!waveNo.value) {
    ElMessage.error('波次ID不能为空')
    return
  }

  loading.value = true

  const params = {
    waveNo: waveNo.value,
    whCode: 'CA',
  }

  try {
    const response = await getWaveDetail(params)

    if (response && response.code === 200) {
      waveDetail.value = response.data || {}
    }
  } catch (error) {
    console.error('获取波次详情失败', error)
    ElMessage.error('获取波次详情失败')
  } finally {
    loading.value = false
  }
}

// 复制波次号
const copyWaveNo = async () => {
  try {
    await navigator.clipboard.writeText(waveDetail.value.waveNo)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败', error)
    ElMessage.error('复制失败')
  }
}

// 处理操作按钮点击
const handleAction = (action) => {
  switch (action.key) {
    case 'assign':
      ElMessage.info('分配拣货员功能待实现')
      break
    case 'start':
      ElMessage.info('开始拣货功能待实现')
      break
    case 'finish':
      ElMessage.info('完成拣货功能待实现')
      break
    case 'print':
      ElMessage.info('打印拣货单功能待实现')
      break
    default:
      console.warn(`未知的操作: ${action.key}`)
  }
}

// 查看订单详情
const handleViewOrder = (order) => {
  ElMessage.info(`查看订单详情: ${order.orderNo}`)
}

// 监听路由参数变化
watch(
  () => route.query.waveNo,
  (newWaveNo) => {
    if (newWaveNo && newWaveNo !== waveNo.value) {
      waveNo.value = newWaveNo
      fetchWaveDetail()
    }
  },
  { immediate: false }
)

// 监听路由参数变化（params方式）
watch(
  () => route.params.waveNo,
  (newWaveNo) => {
    if (newWaveNo && newWaveNo !== waveNo.value) {
      waveNo.value = newWaveNo
      fetchWaveDetail()
    }
  },
  { immediate: false }
)

// 初始化
onMounted(() => {
  fetchWaveDetail()
})
</script>

<template>
  <div class="wave-detail-container" v-loading="loading">
    <!-- 顶部操作栏 -->
    <div class="detail-header">
      <div class="header-title">
        <div class="wave-no-container">
          <span class="wave-no">{{ waveDetail.waveNo }}</span>
          <el-icon @click="copyWaveNo" class="copy-icon">
            <DocumentCopy />
          </el-icon>
          <el-tag :type="getStatusTagType(waveDetail.status)" class="status-tag">
            {{ waveDetail.statusName }}
          </el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          v-for="action in getVisibleDetailActions(waveDetail)"
          :key="action.key"
          :type="action.type"
          @click="handleAction(action)"
        >
          {{ action.label }}
        </el-button>
      </div>
    </div>

    <!-- 基本信息 -->
    <WaveBasicInfo :waveDetail="waveDetail" />

    <!-- 详情标签页 -->
    <el-card class="detail-tabs-card" shadow="hover">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="出库单信息" name="orders">
          <WaveOrderList :waveNo="waveNo" @view-order="handleViewOrder" />
        </el-tab-pane>

        <el-tab-pane label="产品明细" name="skus">
          <WaveSkuDetail :waveNo="waveNo" />
        </el-tab-pane>

        <el-tab-pane label="日志" name="logs">
          <WaveOperationLog :waveNo="waveNo" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style scoped>
.wave-detail-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 120px);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.wave-no-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
}

.wave-no {
  font-weight: 700;
  font-size: 18px;
}

.copy-icon {
  color: var(--el-color-primary);
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s;
}

.copy-icon:hover {
  color: var(--el-color-primary-dark-2);
}

.status-tag {
  margin-left: 4px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.detail-tabs-card {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

:deep(.el-card__body) {
  padding: 0;
}

:deep(.el-tabs) {
  margin: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-tabs__nav-wrap) {
  padding: 16px 0;
}
</style>
