<script setup>
import { ref, defineProps, onMounted } from 'vue'
import { getWaveOperationLog } from '@/api/outbound/wave'
import { ElMessage } from 'element-plus'

const props = defineProps({
  waveNo: {
    type: String,
    required: true,
  },
})

// 表格数据
const loading = ref(false)
const logList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 获取操作日志
const fetchOperationLog = async () => {
  loading.value = true

  try {
    const params = {
      bizNo: props.waveNo,
      whCode: 'CA',
      bizType: 'wave_log',
      current: currentPage.value,
      size: pageSize.value,
    }

    const response = await getWaveOperationLog(params)

    if (response && response.code === 200) {
      logList.value = response.data.records || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取操作日志失败', error)
    ElMessage.error('获取操作日志失败')
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOperationLog()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchOperationLog()
}

// 初始化
onMounted(() => {
  fetchOperationLog()
})
</script>

<template>
  <div class="operation-log-container">
    <el-table :data="logList" :loading="loading" stripe style="width: 100%">
      <el-table-column prop="operateDetail" label="操作类型" />

      <el-table-column prop="operateName" label="操作人" />

      <el-table-column prop="operateTime" label="操作时间" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.operation-log-container {
  padding: 0;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}
</style>
