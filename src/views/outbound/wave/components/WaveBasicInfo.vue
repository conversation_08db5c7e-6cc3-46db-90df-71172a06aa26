<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  waveDetail: {
    type: Object,
    required: true,
  },
})
</script>

<template>
  <el-card class="basic-info-card" shadow="hover">
    <div class="info-grid">
      <div class="info-item">
        <label>波次品种类型</label>
        <span class="value">{{ waveDetail.pickingTypeName }}</span>
      </div>
      <div class="info-item">
        <label>订单数量</label>
        <span class="value">{{ waveDetail.orderCount }}</span>
      </div>
      <div class="info-item">
        <label>总数量</label>
        <span class="value">{{ waveDetail.totalCount }}</span>
      </div>
      <div class="info-item">
        <label>创建人</label>
        <span class="value">{{ waveDetail.auditor }}</span>
      </div>
      <div class="info-item">
        <label>创建时间</label>
        <span class="value">{{ waveDetail.createTime }}</span>
      </div>
      <div class="info-item">
        <label>拣货员</label>
        <span class="value">{{ waveDetail.assigntor || '-' }}</span>
      </div>
    </div>
  </el-card>
</template>

<style scoped>
.basic-info-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.card-header {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px 24px;
  padding: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  font-size: 13px;
  white-space: nowrap;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color-page);
}
</style>
