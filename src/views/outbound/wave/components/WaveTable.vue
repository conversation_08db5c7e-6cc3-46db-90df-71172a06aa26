<script setup>
import { useWaveConfig, useWaveStatus } from '@/composables/useWaveConfig'
import {
  MERGE_STATUS_TEXT,
  OUTBOUND_STATUS_TEXT,
  REVIEW_STATUS_TEXT,
  SORING_STATUS_TEXT,
} from '@/constants/wave'
import { MoreFilled } from '@element-plus/icons-vue'
import { defineEmits, defineProps, ref } from 'vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  waveList: {
    type: Array,
    required: true,
  },
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  tableColumns: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits([
  'selection-change',
  'pagination-change',
  'assign-picker',
  'start-picking',
  'finish-picking',
  'view-detail',
  'wave-download',
])

// 使用配置组合式函数
const { getVisibleRowActions } = useWaveConfig()
const { getStatusTagType, formatTime } = useWaveStatus()

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
  emit('pagination-change', { page: props.currentPage, size })
}

const handleCurrentChange = (page) => {
  emit('pagination-change', { page, size: props.pageSize })
}

// 按钮分组逻辑
const getGroupedActions = (row) => {
  const allActions = getVisibleRowActions(row)
  if (allActions.length <= 1) {
    return {
      primaryActions: allActions,
      moreActions: [],
    }
  }
  return {
    primaryActions: allActions.slice(0, 1),
    moreActions: allActions.slice(1),
  }
}

// 统一的行操作处理
const handleRowAction = (actionKey, row) => {
  const actionMap = {
    view: () => emit('view-detail', row),
    assign: () => emit('assign-picker', row),
    start: () => emit('start-picking', row),
    finish: () => emit('finish-picking', row),
    print_summary: () => emit('wave-download', row, 1), //打印汇总拣货单
    print_sorting: () => emit('wave-download', row, 2), //打印分拣拣货单
  }

  const action = actionMap[actionKey]
  if (action) {
    action()
  } else {
    console.warn(`未知的操作: ${actionKey}`)
  }
}
</script>

<template>
  <el-card class="table-card" shadow="hover">
    <el-table
      :data="waveList"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />

      <!-- 动态渲染表格列 -->
      <template v-for="column in tableColumns" :key="column.prop">
        <!-- 波次单号列 - 特殊处理，显示为链接 -->
        <el-table-column
          v-if="column.prop === 'waveNo'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :fixed="column.fixed"
        >
          <template #default="scope">
            <el-link type="primary" @click="handleRowAction('view', scope.row)">
              {{ scope.row.waveNo }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 面单拼接 -->
        <el-table-column
          v-else-if="column.prop === 'mergeStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ MERGE_STATUS_TEXT[scope.row.mergeStatus] }}
          </template>
        </el-table-column>

        <!-- 二次分拣 -->
        <el-table-column
          v-else-if="column.prop === 'sortingStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ SORING_STATUS_TEXT[scope.row.sortingStatus] }}
          </template>
        </el-table-column>

        <!-- 复核 -->
        <el-table-column
          v-else-if="column.prop === 'reviewStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ REVIEW_STATUS_TEXT[scope.row.reviewStatus] }}
          </template>
        </el-table-column>

        <!-- 出库 -->
        <el-table-column
          v-else-if="column.prop === 'outboundStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ OUTBOUND_STATUS_TEXT[scope.row.outboundStatus] }}
          </template>
        </el-table-column>

        <!-- 拣货员 -->
        <el-table-column
          v-else-if="column.prop === 'assigntor'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.assigntor || '-' }}
          </template>
        </el-table-column>

        <!-- 已拣/应拣数量 -->
        <el-table-column
          v-else-if="column.prop === 'pickedQty'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.picked + '/' + scope.row.needPick }}
          </template>
        </el-table-column>

        <!-- 时间相关列 - 格式化显示 -->
        <el-table-column
          v-else-if="['createTime', 'assignTime', 'startTime', 'finishTime'].includes(column.prop)"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ formatTime(scope.row[column.prop]) }}
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column
          v-else-if="column.prop === 'status'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :fixed="column.fixed"
        >
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 其他普通列 -->
        <el-table-column
          v-else
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
        />
      </template>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <div class="action-buttons">
            <!-- 主要操作按钮（前两个） -->
            <el-button
              v-for="action in getGroupedActions(scope.row).primaryActions"
              :key="action.key"
              :type="action.type"
              :link="action.link"
              @click="handleRowAction(action.key, scope.row)"
            >
              {{ action.label }}
            </el-button>

            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              v-if="getGroupedActions(scope.row).moreActions.length > 0"
              trigger="click"
              @command="(command) => handleRowAction(command, scope.row)"
            >
              <el-button :link="true" type="primary">
                更多
                <el-icon class="el-icon--right">
                  <MoreFilled />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="action in getGroupedActions(scope.row).moreActions"
                    :key="action.key"
                    :command="action.key"
                  >
                    {{ action.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<style scoped>
.table-card {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page) !important;
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600 !important;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}
</style>
