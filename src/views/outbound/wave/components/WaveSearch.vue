<script setup>
import { Search, Refresh } from '@element-plus/icons-vue'
import { defineProps, defineEmits } from 'vue'
import CompositeDatePicker from '@/components/common/CompositeDatePicker.vue'
import {
  BARCODE_TYPE_OPTIONS,
  NUMBER_TYPE_OPTIONS,
  ORDER_TYPE_OPTIONS,
  PICKING_TYPE_OPTIONS,
  ASSIGN_PICKER_OPTIONS,
  SORTING_FLAG_OPTIONS,
  REVIEW_FLAG_OPTIONS,
  OUTBOUND_FLAG_OPTIONS,
  TIME_TYPE_OPTIONS,
} from '@/constants/wave'

const props = defineProps({
  queryParams: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  // 动态获取的选项数据
  logisticsCarrierOptions: {
    type: Array,
    default: () => [],
  },
  logisticsChannelOptions: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['search', 'reset'])

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}

// 获取单号输入框占位符
const getNumberPlaceholder = () => {
  const option = NUMBER_TYPE_OPTIONS.find((item) => item.value === props.queryParams.numberType)
  return option?.placeholder || '请先选择单号类型'
}

// 处理单号类型变化
const handleNumberTypeChange = () => {
  // 清空输入值
  props.queryParams.numberValue = ''
}

// 处理Barcode类型变化
const handleBarcodeTypeChange = () => {
  // 清空输入值
  props.queryParams.barcodeValue = ''
}
</script>

<template>
  <el-card class="search-card" shadow="hover">
    <el-form :model="queryParams" inline>
      <!-- 出库单类型 -->
      <el-form-item prop="orderType" class="search-item">
        <el-select
          v-model="queryParams.orderType"
          placeholder="出库单类型"
          multiple
          collapse-tags
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="option in ORDER_TYPE_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 物流渠道 -->
      <el-form-item prop="logisticsChannel" class="search-item">
        <el-select
          v-model="queryParams.logisticsChannel"
          placeholder="物流渠道"
          multiple
          collapse-tags
          clearable
          style="width: 140px"
        >
          <el-option
            v-for="channel in logisticsChannelOptions"
            :key="channel.value"
            :label="channel.label"
            :value="channel.value"
          />
        </el-select>
      </el-form-item>

      <!-- 承运商 -->
      <el-form-item prop="logisticsCarrier" class="search-item">
        <el-select
          v-model="queryParams.logisticsCarrier"
          placeholder="承运商"
          multiple
          collapse-tags
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="carrier in logisticsCarrierOptions"
            :key="carrier.value"
            :label="carrier.label"
            :value="carrier.value"
          />
        </el-select>
      </el-form-item>

      <!-- 波次品种类型 -->
      <el-form-item prop="pickingType" class="search-item">
        <el-select
          v-model="queryParams.pickingType"
          placeholder="波次品种类型"
          clearable
          style="width: 140px"
        >
          <el-option
            v-for="option in PICKING_TYPE_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 拣货员分配状态 -->
      <el-form-item prop="isAssignPicker" class="search-item">
        <el-select
          v-model="queryParams.isAssignPicker"
          placeholder="拣货员分配状态"
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="option in ASSIGN_PICKER_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 二次分拣状态 -->
      <el-form-item prop="sortingFlag" class="search-item">
        <el-select
          v-model="queryParams.sortingFlag"
          placeholder="二次分拣状态"
          clearable
          style="width: 140px"
        >
          <el-option
            v-for="option in SORTING_FLAG_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 复核状态 -->
      <el-form-item prop="reviewFlag" class="search-item">
        <el-select
          v-model="queryParams.reviewFlag"
          placeholder="复核状态"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="option in REVIEW_FLAG_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 出库状态 -->
      <el-form-item prop="outboundFlag" class="search-item">
        <el-select
          v-model="queryParams.outboundFlag"
          placeholder="出库状态"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="option in OUTBOUND_FLAG_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 复合型单号输入框 - Barcode查询 -->
      <el-form-item prop="barcodeSearch" class="search-item">
        <el-input
          v-model="queryParams.barcodeValue"
          placeholder="请输入Barcode"
          clearable
          style="width: 240px"
        >
          <template #prepend>
            <el-select
              v-model="queryParams.barcodeType"
              style="width: 100px"
              @change="handleBarcodeTypeChange"
            >
              <el-option
                v-for="option in BARCODE_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <!-- 复合型时间日期选择器 -->
      <el-form-item prop="timeRange" class="search-item">
        <CompositeDatePicker
          v-model="queryParams.timeRange"
          :type-options="
            TIME_TYPE_OPTIONS.map((item) => ({
              value: item.value,
              label: item.label,
              dateType: 'daterange',
            }))
          "
          style="width: 320px"
        />
      </el-form-item>

      <!-- 复合型单号输入框 - 单号查询 -->
      <el-form-item prop="numberSearch" class="search-item">
        <el-input
          v-model="queryParams.numberValue"
          :placeholder="getNumberPlaceholder()"
          clearable
          style="width: 240px"
        >
          <template #prepend>
            <el-select
              v-model="queryParams.numberType"
              style="width: 100px"
              @change="handleNumberTypeChange"
            >
              <el-option
                v-for="option in NUMBER_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item class="search-item">
        <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
        <el-button :icon="Refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<style scoped>
.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.search-item {
  margin-bottom: 10px !important;
}

:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-card__body) {
  padding: 16px 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}
</style>
