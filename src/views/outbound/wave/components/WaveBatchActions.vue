<script setup>
import { User, VideoPlay, Check, Printer, Download } from '@element-plus/icons-vue'
import { defineProps, defineEmits, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useWaveConfig } from '@/composables/useWaveConfig'

const props = defineProps({
  selectedRows: {
    type: Array,
    default: () => [],
  },
  pickerOptions: {
    type: Array,
    default: () => [],
  },
  currentTab: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['batch-assign', 'batch-start', 'batch-finish', 'batch-print', 'export'])

// 使用配置组合式函数
const { getVisibleBatchActions } = useWaveConfig()

// 分配拣货员对话框
const assignDialog = ref({
  visible: false,
  pickerCode: '',
})

// 批量分配拣货员
const handleBatchAssign = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要分配的波次')
    return
  }
  assignDialog.value.visible = true
  assignDialog.value.pickerCode = ''
}

// 确认分配
const confirmAssign = () => {
  if (!assignDialog.value.pickerCode) {
    ElMessage.warning('请选择拣货员')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-assign', {
    waveIds,
    pickerCode: assignDialog.value.pickerCode,
  })
  assignDialog.value.visible = false
}

// 批量开始拣货
const handleBatchStart = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要开始拣货的波次')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-start', { waveIds })
}

// 批量完成拣货
const handleBatchFinish = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要完成拣货的波次')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-finish', { waveIds })
}

// 批量打印拣货单
const handleBatchPrint = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要打印的波次')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-print', { waveIds })
}

// 导出数据
const handleExport = () => {
  emit('export')
}

// 统一的批量操作处理
const handleBatchAction = (actionKey) => {
  const actionMap = {
    'batch-assign': handleBatchAssign,
    'batch-start': handleBatchStart,
    'batch-finish': handleBatchFinish,
    'batch-print': handleBatchPrint,
    export: handleExport,
  }

  const action = actionMap[actionKey]
  if (action) {
    action()
  } else {
    console.warn(`未知的批量操作: ${actionKey}`)
  }
}
</script>

<template>
  <el-card class="batch-actions-card" shadow="hover">
    <div class="action-buttons">
      <el-button
        v-for="action in getVisibleBatchActions(currentTab)"
        :key="action.key"
        :type="action.type"
        :disabled="action.key !== 'export' && selectedRows.length === 0"
        @click="handleBatchAction(action.key)"
      >
        {{ action.label }}
      </el-button>
    </div>

    <!-- 分配拣货员对话框 -->
    <el-dialog
      v-model="assignDialog.visible"
      title="批量分配拣货员"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="拣货员">
          <el-select
            v-model="assignDialog.pickerCode"
            placeholder="请选择拣货员"
            style="width: 100%"
          >
            <el-option
              v-for="picker in pickerOptions"
              :key="picker.code"
              :label="picker.name"
              :value="picker.code"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAssign">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<style scoped>
.batch-actions-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

:deep(.el-card__body) {
  padding: 16px 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
