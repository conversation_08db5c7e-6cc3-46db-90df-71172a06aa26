<script setup>
import { ref, defineProps, onMounted } from 'vue'
import { getWaveSkuList } from '@/api/outbound/wave'
import { ElMessage } from 'element-plus'

const props = defineProps({
  waveNo: {
    type: String,
    required: true,
  },
})

// 表格数据
const loading = ref(false)
const skuList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 获取SKU明细
const fetchSkuDetail = async () => {
  loading.value = true

  try {
    const params = {
      waveNo: props.waveNo,
      whCode: 'CA',
      current: currentPage.value,
      size: pageSize.value,
    }

    const response = await getWaveSkuList(params)

    if (response && response.code === 200) {
      skuList.value = response.data.records || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取SKU明细失败', error)
    ElMessage.error('获取SKU明细失败')
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchSkuDetail()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchSkuDetail()
}

// 初始化
onMounted(() => {
  fetchSkuDetail()
})
</script>

<template>
  <div class="sku-detail-container">
    <el-table :data="skuList" :loading="loading" stripe style="width: 100%">
      <el-table-column prop="productSku" label="SKU" />

      <el-table-column prop="productSkuName" label="产品名称" show-overflow-tooltip />

      <el-table-column prop="pickedQty" label="已拣/应拣数量">
        <template #default="scope">
          <span class="qty-text">{{ scope.row.pickQty + '/' + scope.row.planQty }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="stockQualityName" label="库位属性">
        <template #default="scope">
          <span class="qty-text" :class="scope.row.stockQuality === 0 ? 'picked' : ' remain'">{{
            scope.row.stockQualityName
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.sku-detail-container {
  padding: 0;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.qty-text {
  font-size: 14px;
}

.qty-text.total {
  color: var(--el-color-primary);
}

.qty-text.picked {
  color: var(--el-color-success);
}

.qty-text.remain {
  color: var(--el-color-warning);
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-progress__text) {
  font-size: 12px !important;
}
</style>
