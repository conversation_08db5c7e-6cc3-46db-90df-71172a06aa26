<script setup>
// 1. 导入 (Imports)
import {ref, reactive, onMounted} from 'vue'
import {ElMessage, ElMessageBox } from 'element-plus'
import {
  listing,
  options,
  userParams,
  statusSum,
  consumers,
  channels,
  skuList,
  makeException,
  shipment
} from '@/api/outbound/parcel'
import ParcelSearch from './components/ParcelSearch.vue'
import ParcelTable from './components/ParcelTable.vue'

// 2. Props & Emits 定义 (此组件无props和emits)

// 3. 响应式状态 (Reactive State)
// 查询条件
const queryParams = reactive({
  appendixFlag: "",
  categoryIdList: [],
  cellNos: [],
  codeType: "barcode",
  countKind: "orderWeight",
  countryRegionCodes: "",
  current: 1,
  startTime: "",
  endTime: "",
  customerCodes: "",
  expressFlag: "",
  expressPrintStatus: "",
  forecastStatus: "",
  logisticsCarrier: "",
  logisticsChannel: "",
  orderCount: "",
  orderNoType: "sourceNo",
  orderSourceList: [],
  productPackType: "",
  receiver: "",
  receiverValue: "",
  relatedReturnOrder: "",
  salesPlatform: "",
  size: 20,
  skuQtyStrList: [],
  status: "10",
  timeType: "",
  unitMark: 0,
  varietyType: "",
  weightCountEnd: "",
  weightCountStart: "",
  whCode: "",
  withVas: "",
  sourceNoVal: "",
  receiverKey: "receiver",
  receiverVal: "",
  skuKey: "barcode",
  skuVal: "",
  numRangeType: "weight",
  cellNos: [],
})


const dateParams = reactive({
  dateType: 'createTime', // 默认日期类型为创建时间
  dateRange: []
})

// 表格加载状态
const loading = ref(false)

// 表格数据
const outboundList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

const statusType = ref(10) //
const statusSumOptions = ref([])

// 日期类型选项
const dateTypeOptions = ref([])
const orderNoOptions = ref([])
const salesPlatformList = ref([])
const carrierList = ref([])
const countryRegionList = ref([])

const receiverOptions = [
  {value: 'receiver', label: '收件人'},
  {value: 'postCode', label: '收件人邮编'},
]

const unitMarkOptions = [
  {value: 0, label: '公制单位'},
  {value: 1, label: '英制单位'},
]

const skuTypeOptions = [
  {value: 'barcode', label: 'Barcode'},
  {value: 'productName', label: '产品名称'},
]

const numberRangeOptions = [
  {value: 'weight', label: '订单总重量'},
  {value: 'skuNum', label: '产品总数量'},
]

const numberRangeParams = ref({
  'weight': ['weightCountStart', 'weightCountEnd'],
  'skuNum':['skuCountStart', 'skuCountEnd']
})

const numberRangeInput = reactive({
  numberRange:[],
  minValue:0,
  maxValue:0
})

// options
const consumerList = ref([])
const channelList = ref([])
const skuTypeList = ref([])

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 搜索
const handleQuery = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      status: 10
    }


    // 处理日期范围
    if (dateParams.dateRange && dateParams.dateRange.length === 2) {
      const [beginDate, endDate] = dateParams.dateRange
      params.startTime = beginDate
      params.endTime = endDate
      params.timeType = dateParams.dateType
    } else {
      params.timeType = "";
    }

    if (queryParams.sourceNoVal && queryParams.orderNoType) {
      params.orderNoType = queryParams.orderNoType
      var parKey = '';
      orderNoOptions.value.map((item, index) => {
        if (item.key == queryParams.orderNoType) {
          parKey = item.parKey
          return;
        }
      })
      if(parKey){
        params[parKey] = [queryParams.sourceNoVal]
      }
    }

    params.customerCodes = queryParams.customerCodes
    params.varietyType = queryParams.varietyType
    params.salesPlatform = queryParams.salesPlatform
    params.logisticsChannel = queryParams.logisticsChannel
    params.logisticsCarrier = queryParams.logisticsCarrier
    params.countryRegionCodes = queryParams.countryRegionCodes
    params.unitMark = queryParams.unitMark
    params.cellNos = queryParams.cellNos

    //处理 NumberRanger
    if(numberRangeInput.numberRange.length == 2 && queryParams.numRangeType){
      var numberParam = numberRangeParams.value[queryParams.numRangeType]
      params[numberParam[0]] = numberRangeInput.numberRange[0]
      params[numberParam[1]] = numberRangeInput.numberRange[1]
    }

    if (queryParams.receiverVal) {
      params[queryParams.receiverKey] = queryParams.receiverVal
    }

    if (queryParams.skuVal) {
      params[queryParams.skuKey] = queryParams.skuVal
    }

    //重新计算分类数量
    fetchStatusSum(params)

    params.status = statusType.value

    const response = await listing(params)

    if (response && response.data.records.length > 0) {
      outboundList.value = response.data.records || []
      total.value = response.data.total || 0
      currentPage.value = response.data.current || 1
      pageSize.value = response.data.size || 20
    } else {
      outboundList.value = []
      total.value = 0
      if (currentPage.value > 1) {
        currentPage.value = 1
        handleQuery()
      }
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetQuery = () => {
  queryParams.appendixFlag = "",
      queryParams.categoryIdList = [],
      queryParams.cellNos = [],
      queryParams.codeType = "barcode",
      queryParams.countKind = "orderWeight",
      queryParams.countryRegionCodes = "",
      queryParams.current = 1,
      queryParams.startTime = "",
      queryParams.endTime = "",
      queryParams.customerCodes = "",
      queryParams.expressFlag = "",
      queryParams.expressPrintStatus = "",
      queryParams.forecastStatus = "",
      queryParams.logisticsCarrier = "",
      queryParams.logisticsChannel = "",
      queryParams.orderCount = "",
      queryParams.orderNoType = "sourceNo",
      queryParams.orderSourceList = [],
      queryParams.productPackType = "",
      queryParams.receiver = "",
      queryParams.receiverValue = "",
      queryParams.relatedReturnOrder = "",
      queryParams.salesPlatform = "",
      queryParams.size = 20,
      queryParams.skuQtyStrList = [],
      queryParams.status = "10",
      queryParams.timeType = "createTime",
      queryParams.unitMark = 0,
      queryParams.varietyType = "",
      queryParams.weightCountEnd = "",
      queryParams.weightCountStart = "",
      queryParams.whCode = "",
      queryParams.withVas = ""
      queryParams.sourceNoVal= "",
      queryParams.eceiverKey= "",
      queryParams.receiverVal = "",
      queryParams.skuKey = "",
      queryParams.skuVal = "",
      queryParams.cellNos = []

  dateParams.dateRange = []
  numberRangeInput.numberRange = []
  numberRangeInput.minValue = 0
  numberRangeInput.maxValue = 0

  currentPage.value = 1
  pageSize.value = 20

  handleQuery()
}

// 处理分页变化
const handlePaginationChange = ({page, size}) => {
  currentPage.value = page
  pageSize.value = size
  handleQuery()
}


// option consumers
const fetchConsumers = async () => {
  loading.value = true

  try {

    const response = await consumers()

    if (response && response.data) {
      consumerList.value = response.data || []
    }
  } catch (error) {
    console.error('获取客户失败', error)
    ElMessage.error('获取客户失败')
  } finally {
    loading.value = false
  }
};

//打印发货清单
const handlePrintShipment = async (row) => {
  // 定义一个变量来引用 iframe，以便在 onload 事件中访问
  let iframe = null

  try {
    loading.value = true

    const params = {
      customerCode: row.customerCode,
      deliveryNo: row.deliveryNo,
      whCode: row.whCode
    }
    const response = await shipment(params)

    // 修正 1: 确认你使用的是响应中的数据部分 (通常是 response.data)
    // 假设 getWaveDownload 返回的是 axios 响应对象
    const blob = new Blob([response.data], { type: 'application/pdf' })
    // 检查 blob 是否为空，避免创建空的打印页
    if (blob.size === 0) {
      console.error('获取到的PDF文件内容为空')
      // ElMessage.error('无法打印，获取到的文件为空');
      return
    }

    const blobUrl = URL.createObjectURL(blob)
    iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    iframe.src = blobUrl

    // onload 事件是核心
    iframe.onload = () => {
      try {
        iframe.contentWindow.focus()
        iframe.contentWindow.print()
      } catch (e) {
        console.error('调用打印功能失败:', e)
        // ElMessage.error('调用打印功能失败');
      } finally {
        // 修正 2: 在打印任务发起后（无论成功、失败或取消）清理资源
        // a. 撤销 object URL 释放内存
        // URL.revokeObjectURL(blobUrl)
        // b. 从 DOM 中移除 iframe
        // if (iframe) {
        //   document.body.removeChild(iframe)
        // }
      }
    }

    // 将 iframe 添加到 DOM 中，这会触发 src 的加载
    document.body.appendChild(iframe)

  } catch (error) {
    console.error('操作失败', error)
  } finally {
    loading.value = false
  }
}

// 标记异常
const handleMarkException = async (row) => {
  try {
    await ElMessageBox.confirm(`标记异常后，该出库单会被移除波次，需要在【异常件】模块中进行处理，确认继续吗?`, '标记异常', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    loading.value = true

    const params = {
      customerCode: row.customerCode,
      deliveryNo: row.deliveryNo,
      whCode: row.whCode
    }

    const response = await makeException(params)

    if (response && response.code == 200) {
      ElMessage.success('操作成功')
    }
  } catch (error) {
    console.error('操作失败', error)
  } finally {
    loading.value = false
  }
}

// option channels
const fetchChannels = async () => {
  loading.value = true

  try {

    const response = await channels()

    if (response && response.data) {
      channelList.value = response.data || []
    }
  } catch (error) {
    console.error('获取渠道失败', error)
    ElMessage.error('获取渠道失败')
  } finally {
    loading.value = false
  }
}

const fetchOptions = async () => {
  loading.value = true

  try {

    const response = await options()

    if (response && response.data) {
      skuTypeList.value = response.data.orderSkuType || []
      dateTypeOptions.value = response.data.parcelSearchDateType || []
      orderNoOptions.value = response.data.parcelOrderNo || []
      salesPlatformList.value = response.data.salesPlatformList || []
      carrierList.value = response.data.carrierList || []
      countryRegionList.value = response.data.countryRegionList || []
    }
  } catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const fetchStatusSum = async (queryParams = null) => {
  loading.value = true

  try {

    const response = await statusSum(queryParams)

    if (response && response.data) {
      statusSumOptions.value = response.data || []
    }
  } catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const initDateRange = async () => {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)

  console.log(start)
  console.log(end)

  dateParams.dateRange = [start, end];
}


// 初始化
onMounted(() => {
  handleQuery()

  fetchConsumers()
  fetchChannels()
  fetchOptions()
  // initDateRange()
})
</script>

<template>
  <div class="user-container">

    <!-- 搜索区域 -->
    <ParcelSearch :queryParams="queryParams" :dateTypeOptions="dateTypeOptions" :orderNoOptions="orderNoOptions"
                  :consumerList="consumerList" :channelList="channelList" :skuTypeList="skuTypeList"
                  :dateParams="dateParams" :salesPlatformList="salesPlatformList" :carrierList="carrierList"
                  :countryRegionList="countryRegionList" :receiverOptions="receiverOptions"
                  :unitMarkOptions="unitMarkOptions" :skuTypeOptions="skuTypeOptions"
                  :numberRangeOptions="numberRangeOptions" :numberRangeInput="numberRangeInput"
                  @search="handleQuery" @reset="resetQuery"/>

    <el-tabs v-model="statusType" @tab-change="handleQuery" class="warehouse-tabs">
      <el-tab-pane label="全部" name=""/>
      <el-tab-pane
          v-for="item in statusSumOptions"
          :key="item.status"
          :label="item.statusNameCount"
          :name="item.status"
      />
    </el-tabs>
    <!-- 表格 -->
    <ParcelTable :loading="loading" :outboundList="outboundList" :total="total" :currentPage="currentPage"
                 :pageSize="pageSize"
                 @pagination-change="handlePaginationChange" :statusType="statusType"
                 @make-exception="handleMarkException" @print-shipment="handlePrintShipment"
                 @selection-change="handleSelectionChange"/>
  </div>
</template>

<style scoped>
.user-container {
  padding: 20px;
}
</style>
