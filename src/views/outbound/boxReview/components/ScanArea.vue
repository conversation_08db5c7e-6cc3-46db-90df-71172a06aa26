<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Setting } from '@element-plus/icons-vue'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    barcodeDisabled: {
      type: Boolean,
      default: true
    },
    orderNoDisabled: {
      type: Boolean,
      default: false
    },
})

const emit = defineEmits(['scan', 'sku-scan', 'setting', 'weight-finish'])
const orderNo = ref('')
const barcode = ref('')

// SCAN
const handleScan = () => {
  emit('scan', orderNo.value)
}

// SKU SCAN
const handleSkuScan = () => {
  emit('sku-scan', barcode.value)
}

//SETTING
const showSetting = () => {
    emit('setting')
}


</script>

<template>
    <el-card class="scan-card" shadow="hover">
        <template #header>
            <div class="card-header">
                <span style="font-size: 24px;">包裹复核</span>
            </div>
        </template>

        <div class="scan-area">
          <el-row style="font-weight: bold">
            波次单号
          </el-row>
          <el-row>
              <el-input
                  v-model="orderNo" :disabled="orderNoDisabled"
                  style="width: 280px; height: 40px; margin-top: 10px;"
                  placeholder="请扫描"
                  clearable
                  @keyup.enter="handleScan"
              />
          </el-row>
          <el-row style="font-weight: bold; margin-top: 20px;">
            Barcode
          </el-row>
          <el-row>
            <el-input
                v-model="barcode"
                :disabled="barcodeDisabled"
                style="width: 280px; height: 40px; margin-top: 10px;"
                placeholder="请扫描"
                clearable
                @keyup.enter="handleSkuScan"
            />
          </el-row>


          <div  class="footer">
            <el-select disabled="" placeholder="请选择打印机" style="width: 200px;">
              <el-option key="1" value="1">请选择打印机</el-option>
            </el-select>
            <el-link style="margin-left: 10px;">
              <el-icon @click="showSetting">
                <Setting />
              </el-icon>
            </el-link>
          </div>
        </div>

    </el-card>


</template>

<style scoped>
.scan-card {
    border-radius: 8px;
    height: calc(100vh - 120px);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: none;
}

.scan-area {
  position: relative;
  height: 70vh;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: 0px;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 500;
}


</style>