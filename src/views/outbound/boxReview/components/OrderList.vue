<script setup>
import { defineProps, ref, defineEmits } from 'vue'
import {useRouter} from "vue-router";
import { DocumentCopy } from '@element-plus/icons-vue'

const router = useRouter()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  orderList: {
    type: Array,
    default: () => [],
  },
  skuList: {
    type: Array,
    default: () => [],
  },
  skuData: {
    type: Array,
    default: () => [],
  },
  packList: {
    type: Array,
    default: () => [],
  },
  unReviewedPackList: {
    type: Array,
    default: () => [],
  },
  reviewedPackList: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['print-order'])

const activeName = ref('pack')

//复制
const copy = async (val) => {
  await navigator.clipboard.writeText(val);
  ElMessage.success('复制成功')
}

//打印面单
const handlePrintOrder = async (row) => {
  emit('print-order', row)
}

const handleDetail = (row) => {
  router.push({
    path: '/outbound/detail',
    query:  {deliveryNo: row.deliveryNo, customerCode:row.customerCode, whCode:props.orderList.whCode, sourceNo: row.sourceNo},
  })
}

</script>

<template>
  <div class="profile-container" v-if="orderList.waveNo" v-loading="loading">
    <el-form label-width="100px" label-position="left" >
      <el-card class="profile-card" shadow="hover" >
        <div class="app-container">
          <span style="font-size: 22px; font-weight: bolder;">
            {{ orderList.waveNo }}
          </span>
          <span style="float:right;">
            <label class="orderLab">订单进度 </label>
            <label class="orderReviewNumLab">{{ orderList.reviewOrderNum }}</label>/
            <label class="orderNumLab">{{ orderList.orderNum }}</label>
            <label class="orderLab"> 包裹进度 </label>
            <label class="orderReviewNumLab">{{ orderList.reviewPackageNum }}</label>/
            <label class="orderNumLab">{{ orderList.packageNum }}</label>
            <label class="orderLab"> 产品进度 </label>
            <label class="orderReviewNumLab">{{ orderList.reviewSkuNum }}</label>/
            <label class="orderNumLab">{{ orderList.skuNum }}</label>
          </span>
        </div>
        <div class="app-container">
          <el-table :data="skuList" style="width: 100%;margin-top: 20px;" v-if="skuList.length > 0">
            <el-table-column label="图片" type="index" width="150">
              <template #default="scope">
                <img :src="scope.row.productUrl" />
              </template>
            </el-table-column>
            <el-table-column label="SKU / 产品条码 / 产品名称" prop="" >
              <template #default="scope">
                <span style="font-size: 18px; font-weight: bold; color: black;">
                  {{ scope.row.productSku }}
                </span><br>
                {{ scope.row.barcode }} <br>
                {{ scope.row.productName }}
              </template>
            </el-table-column>
            <el-table-column label="出库单号 / 物流跟踪号" prop="" >
              <template #default="scope">
                <span style="font-size: 18px; font-weight: bold; color: black;">
                  {{ scope.row.sourceNo }}
                </span><br>
                {{ scope.row.expressNo }}
              </template>
            </el-table-column>
            <el-table-column label="已分拣 / 总数量" prop="" >
              <template #default="scope">
                <span style="font-size: 18px; font-weight: bold; color: green;">{{ scope.row.packSerial }}</span>
                /
                <span style="font-size: 18px; font-weight: bold;">{{ scope.row.allQty }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div style="align-items: center; display: flex; height: 25%;
            justify-content: center; color: #606266; margin-top: 10%;"  v-else>
            <img src="/scan.svg" />
            <label>
              请扫描产品
            </label>
          </div>
        </div>
        <div class="app-container" style="margin-top: 20px;">
          <el-tabs v-model="activeName">
            <el-tab-pane label="全部包裹" name="pack">
              <el-table :data="packList" style="width: 100%">
                <el-table-column label="序号" prop="" type="index" width="80px;"/>
                <el-table-column label="物流跟踪号" prop="expressNo" width="220" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.expressNo">
                      {{scope.row.expressNo}}
                      <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                               :icon="DocumentCopy" @click="copy(scope.row.expressNo)">
                      </el-link>
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="出库单号" prop="sourceNo" width="200" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.sourceNo">
                    <el-link type="primary" v-if="scope.row.sourceNo"  @click="() => handleDetail(scope.row)">
                        {{ scope.row.sourceNo }}
                      </el-link>
                    <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                             :icon="DocumentCopy" @click="copy(scope.row.sourceNo)">
                    </el-link>
                  </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="SKU" prop="" width="200px;" >
                  <template #default="scope">
                    <span v-if="scope.row.skuList.length < 2"
                          v-for="sku in scope.row.skuList" >
                      {{sku.sku ? sku.sku : '-'}}
                      <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                               :icon="DocumentCopy" @click="copy(sku.sku)">
                      </el-link>
                    </span>
                      <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
                        <template #default>
                          <el-table :data="scope.row.skuList">
                            <el-table-column label="SKU" prop="sku" width="200" >
                              <template #default="st">
                                {{ st.row.sku }}
                                <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                                         :icon="DocumentCopy" @click="copy(st.row.sku)">
                                </el-link>
                              </template>
                            </el-table-column>
                            <el-table-column label="已扫/总数量" prop="" width="150" >
                              <template #default="item">
                                {{ item.row.scanQty }} / {{ item.row.qty }}
                              </template>
                            </el-table-column>
                          </el-table>
                        </template>
                        <template #reference>
                          <el-tag>多个({{ scope.row.skuList.length }})</el-tag>
                        </template>
                      </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="已扫/总数量" prop="" width="120px;">
                  <template #default="scope">
                    {{scope.row.scanQty}} / {{scope.row.allQty}}
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="">
                  <template #default="scope">
                    <span v-if="scope.row.reviewStatus == 0">未复核</span>
                    <span v-else-if="scope.row.reviewStatus == 1">已复核</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" prop="" >
                  <template #default="scope">
                    <el-button link type="primary" @click="handlePrintOrder(scope.row)">
                      打印面单
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="'已复核' +reviewedPackList.length" name="reviewed">
              <el-table :data="reviewedPackList" style="width: 100%">
                <el-table-column label="序号" prop="" type="index" width="80px;"/>
                <el-table-column label="物流跟踪号" prop="expressNo" width="220" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.expressNo">
                      {{ scope.row.expressNo }}
                      <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                               :icon="DocumentCopy" @click="copy(scope.row.expressNo)">
                      </el-link>
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="出库单号" prop="sourceNo" width="200" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.sourceNo">
                    <el-link type="primary" v-if="scope.row.sourceNo"  @click="() => handleDetail(scope.row)">
                        {{ scope.row.sourceNo }}
                      </el-link>
                    <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                             :icon="DocumentCopy" @click="copy(scope.row.sourceNo)">
                    </el-link>
                  </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="SKU" prop="" width="150px;" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.skuList.length < 2"
                          v-for="sku in scope.row.skuList" >{{sku.sku ? sku.sku : '-'}}
                    </span>
                    <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
                      <template #default>
                        <el-table :data="scope.row.skuList">
                          <el-table-column label="SKU" prop="sku" width="150" show-overflow-tooltip/>
                          <el-table-column label="已扫/总数量" prop="" width="150" >
                            <template #default="item">
                              {{ item.row.scanQty }} / {{ item.row.qty }}
                            </template>
                          </el-table-column>
                        </el-table>
                      </template>
                      <template #reference>
                        <el-tag>多个({{ scope.row.skuList.length }})</el-tag>
                      </template>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column label="已扫/总数量" prop="" width="120px;">
                  <template #default="scope">
                    {{scope.row.scanQty}} / {{scope.row.allQty}}
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="">
                  <template #default="scope">
                    <span v-if="scope.row.reviewStatus == 0">未复核</span>
                    <span v-else-if="scope.row.reviewStatus == 1">已复核</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" prop="" >
                  <template #default="scope">
                      <el-button link type="primary" @click="handlePrintOrder(scope.row)">
                        打印面单
                      </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="'未复核' + unReviewedPackList.length" name="unreview">
              <el-table :data="unReviewedPackList" style="width: 100%">
                <el-table-column label="序号" prop="" type="index" width="80px;"/>
                <el-table-column label="物流跟踪号" prop="expressNo" width="220" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.expressNo">
                      {{ scope.row.expressNo }}
                      <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                               :icon="DocumentCopy" @click="copy(scope.row.expressNo)">
                      </el-link>
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="出库单号" prop="sourceNo" width="200" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.sourceNo">
                    <el-link type="primary" v-if="scope.row.sourceNo"  @click="() => handleDetail(scope.row)">
                        {{ scope.row.sourceNo }}
                      </el-link>
                    <el-link type="primary" style="margin-left: 5px;margin-bottom: 15px;"
                             :icon="DocumentCopy" @click="copy(scope.row.sourceNo)">
                    </el-link>
                  </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="SKU" prop="" width="150px" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.skuList.length < 2"
                          v-for="sku in scope.row.skuList" >{{sku.sku ? sku.sku : '-'}}
                    </span>
                    <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
                      <template #default>
                        <el-table :data="scope.row.skuList">
                          <el-table-column label="SKU" prop="sku" width="150" show-overflow-tooltip/>
                          <el-table-column label="已扫/总数量" prop="" width="150" >
                            <template #default="item">
                              {{ item.row.scanQty }} / {{ item.row.qty }}
                            </template>
                          </el-table-column>
                        </el-table>
                      </template>
                      <template #reference>
                        <el-tag>多个({{ scope.row.skuList.length }})</el-tag>
                      </template>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column label="已扫/总数量" prop="" width="120px;">
                  <template #default="scope">
                    {{scope.row.scanQty}} / {{scope.row.allQty}}
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="">
                  <template #default="scope">
                    <span v-if="scope.row.reviewStatus == 0">未复核</span>
                    <span v-else-if="scope.row.reviewStatus == 1">已复核</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" prop="" >
                  <template #default="scope">
                      <el-button link type="primary" @click="handlePrintOrder(scope.row)">
                        打印面单
                      </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>

    </el-form>
  </div>

  <div class="defaultTips"  v-else>
    <img src="/scan.svg" />
    <label>
      请扫描波次单
    </label>
  </div>
</template>

<style>

.orderReviewNumLab {
  font-size: 18px; font-weight: bold; color: green;
}

.orderNumLab {
  font-size: 18px; font-weight: bold;
}

.orderLab {
  color: #9ca3af; font-size: 14px;
}

.defaultTips {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
  height: 100vh;
}

</style>
