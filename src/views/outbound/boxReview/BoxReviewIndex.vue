<script setup>
import {ref, onMounted, reactive} from 'vue'
import {ElMessage} from 'element-plus'
import {brScan, bcScan, getPrinter, setPrinter} from '@/api/outbound/boxReview'
import ScanArea from './components/ScanArea.vue'
import OrderList from './components/OrderList.vue'
import SettingDialog from './components/SettingDialog.vue'

// 表格数据
const orderList = ref([])
const packList = ref([])
const reviewedPackList = ref([])
const unReviewedPackList = ref([])
const skuData = ref([])
const skuList = ref([])

// 表格加载状态
const loading = ref(false)
const orderNoDisabled = ref(false)
const barcodeDisabled = ref(true)

const printData = ref([])

const settingDialog = reactive({
  visible: false,
  title: '按包裹复核',
})

const printDialog = reactive({
  visible: false,
  title: '',
})

const settingData = ref({
  isOutbound: false,        //所有包裹都复核完成后，波次内所有出库单自动出库
  isPrint: false,        //复核后立即打印物流面单
  isPrintRepeatTip: false,       //重复打印提醒
})

// 获取订单列表
const getList = async (orderNo) => {
  loading.value = true

  try {
    const params = {
      waveNo: orderNo
    }

    const res = await brScan(params)
    if (res.code === 200 && res.data) {
      orderList.value = res.data || []
      packList.value = res.data.packageList || []

      packList.value.map((item) => {
        if (item.reviewStatus == 0) {   //未复核
          unReviewedPackList.value.push(item)
        } else {
          reviewedPackList.value.push(item)
        }
      })

      barcodeDisabled.value = false;
      orderNoDisabled.value = true;

      // if(settingData.value.isPrintRepeatTip && packList.value.length > 0  && packList.value[0].expressPrintStatus == "20") {
      //
      //   const printRow = {
      //     sourceNo: orderNo,
      //     printStatusName: '已打印',
      //   }
      //   printData.value.push(printRow)
      //
      //   printDialog.visible = true
      // }
    } else {
      orderList.value = []
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}

// 获取 SKU 列表
const getSkuList = async (barcode) => {
  loading.value = true

  try {
    const params = {
      barcode: barcode,
      waveNo: orderList.value.waveNo,
      whCode: orderList.value.whCode
    }

    const res = await bcScan(params)
    if (res.code === 200 && res.data) {
      skuData.value = res.data || []
      skuList.value = res.data.skuList || []
    } else {
      skuData.value = []
      skuList.value = []
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}


const handleScan = (orderNo) => {
  getList(orderNo)
}

const handleSkuScan = (barcode) => {
  getSkuList(barcode)
}

const handleSetting = () => {
  settingDialog.visible = true
}

// 设置加载状态
const setLoading = (isLoading) => {
  loading.value = isLoading
}

// 打印面单
const handlePrintOrder = (row) => {
  alert('打印面单 - ' + row.sourceNo)
}

const settingCancel = () => {
  settingDialog.visible = false
}

const settingSuccess = () => {
  settingDialog.visible = false;

  setUserPrinter()
}

const printCancel = () => {
  printData.value = []

  printDialog.visible = false
}

const printSuccess = () => {
  printData.value = []
  printDialog.visible = false

}

//获取用户打印设置
const getUserPrinter = async () => {
  try {
    const res = await getPrinter()
    if (res.code === 200 && res.data) {
      settingData.value.isOutbound = res.data.isOutbound
      settingData.value.isPrint = res.data.isPrint
      settingData.value.isPrintRepeatTip = res.data.isPrintRepeatTip
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

//用户打印设置
const setUserPrinter = async () => {
  try {
    const params = {
      isOutbound: settingData.value.isOutbound,
      isPrint: settingData.value.isPrint,
      isPrintRepeatTip: settingData.value.isPrintRepeatTip
    }

    await setPrinter(params)
  } catch (error) {
    console.error('获取数据失败', error)
  }
}


// 初始化
onMounted(() => {
  getUserPrinter();
})
</script>

<template>
  <div class="swap-container">
    <el-row :gutter="20">
      <!-- 左侧扫描区域 -->
      <el-col :span="6">
        <ScanArea :loading="loading" :barcodeDisabled="barcodeDisabled" :orderNoDisabled="orderNoDisabled"
                  @scan="handleScan" @sku-scan="handleSkuScan" @setting="handleSetting"/>
      </el-col>

      <!-- 右侧订单区域 -->
      <el-col :span="18">
        <OrderList :loading="loading" :orderList="orderList" :skuList="skuList" :skuData="skuData" :packList="packList"
                   :unReviewedPackList="unReviewedPackList" :reviewedPackList="reviewedPackList"
                   @loading-change="setLoading" @print-order="handlePrintOrder"/>
      </el-col>
    </el-row>

    <!-- Setting -->
    <SettingDialog :visible="settingDialog.visible" :title="settingDialog.title" :settingData="settingData"
                   @cancel="settingCancel" @success="settingSuccess"/>

    <!-- 打印 -->
    <PrintDialog :visible="printDialog.visible" :printData="printData"
                 @cancel="printCancel" @success="printSuccess"/>
  </div>
</template>

<style scoped>
.swap-container {
  padding: 20px;
  height: 100%;
}
</style>