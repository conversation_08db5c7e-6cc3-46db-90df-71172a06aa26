<template>
  <el-card class="batch-actions-card" shadow="hover">
    <div class="batch-actions-container">
      <div class="action-buttons">
        <template v-for="action in visibleActions" :key="action.key">
          <!-- 有子项的按钮显示为下拉菜单 -->
          <el-dropdown
            v-if="action.children && action.children.length > 0"
            @command="handleBatchAction"
          >
            <el-button :type="action.type" :disabled="!canExecuteAction(action)">
              {{ action.label }}
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="child in action.children.filter((c) => c.visible)"
                  :key="child.key"
                  :command="child.key"
                  :disabled="!canExecuteAction(child)"
                >
                  {{ child.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 没有子项的按钮直接显示 -->
          <el-button
            v-else
            :type="action.type"
            :disabled="!canExecuteAction(action)"
            @click="handleBatchAction(action.key)"
          >
            {{ action.label }}
          </el-button>
        </template>
      </div>
    </div>
  </el-card>
</template>

<script>
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

export default {
  name: 'AbnormalBatchActions',
  components: {
    ArrowDown,
  },
  props: {
    selectedRows: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['remove_exception', 'check_export', 'filter_export'],
  data() {
    return {
      // 批量操作按钮配置
      batchActions: [
        {
          key: 'remove_exception',
          label: '移除异常',
          type: 'default',
          visible: true,
          needSelection: true,
        },
        {
          key: 'export',
          label: '导出',
          type: 'default',
          visible: true,
          needSelection: false,
          children: [
            {
              key: 'check_export',
              label: '按勾选数据',
              visible: true,
              needSelection: true,
            },
            {
              key: 'filter_export',
              label: '按筛选范围',
              visible: true,
              needSelection: false,
            },
          ],
        },
      ],
    }
  },
  computed: {
    // 可见的操作按钮
    visibleActions() {
      return this.batchActions.filter((action) => action.visible)
    },
  },
  methods: {
    // 检查是否可以执行操作
    canExecuteAction(action) {
      if (action.needSelection) {
        return this.selectedRows.length > 0
      }
      return true
    },

    // 查找操作配置（包括子项）
    findAction(actionKey) {
      for (const action of this.batchActions) {
        if (action.key === actionKey) {
          return action
        }
        if (action.children) {
          const childAction = action.children.find((child) => child.key === actionKey)
          if (childAction) {
            return childAction
          }
        }
      }
      return null
    },

    // 处理批量操作
    async handleBatchAction(actionKey) {
      const action = this.findAction(actionKey)
      if (!action) {
        console.warn(`未找到操作: ${actionKey}`)
        return
      }

      if (!this.canExecuteAction(action)) {
        ElMessage.warning('请先选择要操作的数据')
        return
      }

      switch (actionKey) {
        case 'remove_exception':
          this.handleRemoveException()
          break
        case 'check_export':
          this.handleCheckExport()
          break
        case 'filter_export':
          this.handleFilterExport()
          break
        default:
          console.warn(`未知的批量操作: ${actionKey}`)
      }
    },

    // 移除异常
    async handleRemoveException() {
      if (this.selectedRows.length === 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      const data = {
        ids: this.selectedRows.map((row) => row.id),
      }
      this.$emit('remove_exception', data)
    },

    // 按勾选数据导出
    async handleCheckExport() {
      if (this.selectedRows.length === 0) {
        ElMessage.warning('请至少选择一条数据')
        return
      }
      const data = {
        ids: this.selectedRows.map((row) => row.id),
      }
      this.$emit('check_export', data)
    },

    // 按筛选数据导出
    async handleFilterExport() {
      // 按筛选数据导出不需要传递选中行ID，而是使用当前的筛选条件
      // 筛选条件由父组件维护，这里只需要触发事件
      this.$emit('filter_export')
    },
  },
}
</script>

<style scoped>
.batch-actions-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.batch-actions-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.dropdown-icon {
  margin-left: 4px;
  font-size: 12px;
}
</style>
