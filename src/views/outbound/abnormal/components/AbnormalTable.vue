<template>
  <el-card class="table-card" shadow="hover">
    <el-table
      ref="abnormalTable"
      v-loading="loading"
      :data="abnormalList"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <el-table-column type="selection" width="55" fixed="left" />

      <!-- 基础信息列 -->
      <el-table-column
        v-for="column in tableColumns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="{ row }">
          <!-- 状态标签 -->
          <el-tag v-if="column.prop === 'status'" :type="getStatusTagType(row.status)" size="small">
            {{ row.status }}
          </el-tag>

          <!-- 出库类型 -->
          <span v-else-if="column.prop === 'outboundType'">
            {{ getOutboundTypeText(row.outboundType) }}
          </span>

          <!-- 时间格式化 -->
          <span v-else-if="column.prop.includes('Time')">
            {{ formatTime(row[column.prop]) }}
          </span>

          <!-- 默认显示 -->
          <span v-else>
            {{ row[column.prop] || '-' }}
          </span>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            v-for="action in getVisibleRowActions(row)"
            :key="action.key"
            :type="action.type"
            :size="action.size || 'small'"
            @click="handleRowAction(action.key, row)"
          >
            {{ action.label }}
          </el-button>

          <!-- 更多操作下拉菜单 -->
          <el-dropdown
            v-if="getMoreActions(row).length > 0"
            @command="(command) => handleRowAction(command, row)"
          >
            <el-button type="primary" size="small">
              更多
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="action in getMoreActions(row)"
                  :key="action.key"
                  :command="action.key"
                >
                  {{ action.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script>
import { MoreFilled } from '@element-plus/icons-vue'

export default {
  name: 'AbnormalTable',
  components: {
    MoreFilled,
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    abnormalList: {
      type: Array,
      required: true,
    },
    total: {
      type: Number,
      default: 0,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 20,
    },
  },
  emits: ['selection-change', 'pagination-change', 'row-action'],
  data() {
    return {
      // 表格列配置
      tableColumns: [
        { prop: 'sourceNo', label: '出库单号', width: 160, fixed: 'left' },
        { prop: 'platformOrderNo', label: '平台单号', width: 160 },
        { prop: 'waveNo', label: '波次号', width: 140 },
        { prop: 'waveNo', label: 'SKU * 数量', width: 180 },
        { prop: 'customerName', label: '客户', width: 120 },
        { prop: 'customerName', label: '订单类型', width: 120 },
        { prop: 'logisticsChannel', label: '物流渠道', width: 160 },
        { prop: 'expressNo', label: '物流跟踪号', width: 180 },
        { prop: 'exceptionTime', label: '异常时间', width: 160 },
      ],
      // 行操作按钮配置
      rowActions: [
        {
          key: 'view',
          label: '查看',
          type: 'primary',
          visible: () => true,
        },
        {
          key: 'process',
          label: '处理',
          type: 'warning',
          visible: (row) => row.status === '待处理',
        },
        {
          key: 'edit',
          label: '编辑',
          type: 'default',
          visible: (row) => row.status !== '已处理',
        },
        {
          key: 'delete',
          label: '删除',
          type: 'danger',
          visible: (row) => row.status === '待处理',
        },
      ],
      // 状态标签类型映射
      statusTagTypeMap: {
        待处理: 'warning',
        处理中: 'primary',
        已处理: 'success',
        已取消: 'info',
      },
      // 出库类型文本映射
      outboundTypeTextMap: {
        '': '全部',
        1: '一件代发',
        2: '备货中转',
      },
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      return this.statusTagTypeMap[status] || 'info'
    },

    // 获取出库类型文本
    getOutboundTypeText(type) {
      return this.outboundTypeTextMap[type] || type
    },

    // 格式化时间
    formatTime(time) {
      return time || '-'
    },

    // 获取可见的行操作按钮（前两个）
    getVisibleRowActions(row) {
      const visibleActions = this.rowActions.filter((action) => action.visible(row))
      return visibleActions.slice(0, 2)
    },

    // 获取更多操作按钮（第三个及以后）
    getMoreActions(row) {
      const visibleActions = this.rowActions.filter((action) => action.visible(row))
      return visibleActions.slice(2)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },

    // 处理页面大小变化
    handleSizeChange(size) {
      this.$emit('pagination-change', { page: this.currentPage, size })
    },

    // 处理当前页变化
    handleCurrentChange(page) {
      this.$emit('pagination-change', { page, size: this.pageSize })
    },

    // 处理行操作
    handleRowAction(action, row) {
      this.$emit('row-action', { action, row })
    },
  },
}
</script>

<style scoped>
.table-card {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px 20px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}
</style>
