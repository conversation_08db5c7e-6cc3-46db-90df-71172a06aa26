<template>
  <el-card class="search-card" shadow="hover">
    <el-form
      ref="searchForm"
      :model="queryParams"
      :inline="true"
      label-width="auto"
      class="search-form"
    >
      <el-form-item>
        <el-radio-group v-model="queryParams.outboundType" @change="handleSearch">
          <el-radio-button value="">全部</el-radio-button>
          <el-radio-button :value="1">一件代发</el-radio-button>
          <el-radio-button :value="2">备货中转</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="queryParams.logisticsChannel"
          placeholder="物流渠道"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
          style="width: 180px"
        >
          <el-option
            v-for="item in logisticsChannelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="queryParams.logisticsCarrier"
          placeholder="承运商"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
          style="width: 180px"
        >
          <el-option
            v-for="item in logisticsCarrierOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="queryParams.customerCodes"
          placeholder="客户名称/代码"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
          style="width: 180px"
        >
          <el-option
            v-for="item in customerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <CompositeDatePicker v-model="queryParams.timeRange" :typeOptions="timeTypeOptions" />
      </el-form-item>

      <el-form-item>
        <el-input
          v-model="queryParams.multiKeyword"
          :placeholder="getSearchPlaceholder()"
          clearable
          @keyup.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prepend>
            <el-select v-model="queryParams.searchType" placeholder="类型" style="width: 120px">
              <el-option
                v-for="item in searchTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { Search, Refresh } from '@element-plus/icons-vue'
import CompositeDatePicker from '@/components/common/CompositeDatePicker.vue'

export default {
  name: 'AbnormalSearch',
  components: {
    Search,
    Refresh,
    CompositeDatePicker,
  },
  props: {
    queryParams: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    customerOptions: {
      type: Array,
      default: () => [],
    },
    logisticsChannelOptions: {
      type: Array,
      default: () => [],
    },
    logisticsCarrierOptions: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['search', 'reset'],
  data() {
    return {
      // 时间类型选项
      timeTypeOptions: [
        { value: 'createTime', label: '创建时间', dateType: 'daterange' },
        { value: 'exceptionTime', label: '异常时间', dateType: 'daterange' },
      ],
      // 搜索类型选项
      searchTypeOptions: [
        { value: 'barcode', label: 'Barcode' },
        { value: 'platformOrderNo', label: '平台单号' },
        { value: 'waveNo', label: '波次号' },
        { value: 'sourceNo', label: '出库单号' },
        { value: 'expressNo', label: '跟踪号' },
      ],
    }
  },
  methods: {
    // 获取搜索占位符
    getSearchPlaceholder() {
      const option = this.searchTypeOptions.find(
        (item) => item.value === this.queryParams.searchType,
      )
      return option ? `请输入${option.label}` : '请输入搜索内容'
    },
    // 搜索
    handleSearch() {
      this.$emit('search')
    },
    // 重置
    handleReset() {
      this.$emit('reset')
    },
  },
}
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.search-form {
  margin: 0;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}
</style>
