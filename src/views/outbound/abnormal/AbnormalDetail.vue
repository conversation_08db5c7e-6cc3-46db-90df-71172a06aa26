<template>
  <div class="abnormal-detail-container" v-loading="loading">
    <!-- 顶部操作栏 -->
    <div class="detail-header">
      <div class="header-title">
        <div class="abnormal-info-container">
          <span class="abnormal-barcode">{{ abnormalDetail.barcode }}</span>
          <el-icon @click="copyBarcode" class="copy-icon">
            <DocumentCopy />
          </el-icon>
          <el-tag :type="getStatusTagType(abnormalDetail.status)" class="status-tag">
            {{ abnormalDetail.status }}
          </el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          v-for="action in visibleDetailActions"
          :key="action.key"
          :type="action.type"
          @click="handleAction(action.key)"
        >
          {{ action.label }}
        </el-button>
      </div>
    </div>

    <!-- 基本信息 -->
    <el-card class="basic-info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>条码：</label>
            <span>{{ abnormalDetail.barcode || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>平台单号：</label>
            <span>{{ abnormalDetail.platformOrderNo || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>波次号：</label>
            <span>{{ abnormalDetail.waveNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>出库单号：</label>
            <span>{{ abnormalDetail.sourceNo || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>跟踪号：</label>
            <span>{{ abnormalDetail.expressNo || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>客户名称：</label>
            <span>{{ abnormalDetail.customerName || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>物流渠道：</label>
            <span>{{ abnormalDetail.logisticsChannel || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>承运商：</label>
            <span>{{ abnormalDetail.logisticsCarrier || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>出库类型：</label>
            <span>{{ getOutboundTypeText(abnormalDetail.outboundType) }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 异常信息 -->
    <el-card class="exception-info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>异常信息</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>异常类型：</label>
            <span>{{ abnormalDetail.exceptionType || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>异常时间：</label>
            <span>{{ abnormalDetail.exceptionTime || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ abnormalDetail.createTime || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="info-item">
            <label>异常原因：</label>
            <span>{{ abnormalDetail.exceptionReason || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="info-item">
            <label>备注：</label>
            <span>{{ abnormalDetail.remark || '-' }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 处理对话框 -->
    <el-dialog
      v-model="processDialog.visible"
      title="处理异常件"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="processForm"
        :model="processDialog.form"
        label-width="100px"
      >
        <el-form-item
          label="处理方式"
          prop="processType"
          :rules="[{ required: true, message: '请选择处理方式', trigger: 'change' }]"
        >
          <el-select
            v-model="processDialog.form.processType"
            placeholder="请选择处理方式"
            style="width: 100%"
          >
            <el-option
              v-for="item in processTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item
          label="处理备注"
          prop="remark"
        >
          <el-input
            v-model="processDialog.form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="processDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmProcess">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import { getAbnormalDetail } from '@/api/outbound/abnormal'

export default {
  name: 'AbnormalDetail',
  components: {
    DocumentCopy
  },
  data() {
    return {
      // 加载状态
      loading: false,
      
      // 异常件详情数据
      abnormalDetail: {},
      
      // 异常件ID
      abnormalId: this.$route.query.id,
      
      // 详情页操作按钮配置
      detailActions: [
        {
          key: 'process',
          label: '处理',
          type: 'primary',
          visible: (detail) => detail.status === '待处理'
        },
        {
          key: 'edit',
          label: '编辑',
          type: 'warning',
          visible: (detail) => detail.status !== '已处理'
        },
        {
          key: 'delete',
          label: '删除',
          type: 'danger',
          visible: (detail) => detail.status === '待处理'
        }
      ],
      
      // 状态标签类型映射
      statusTagTypeMap: {
        '待处理': 'warning',
        '处理中': 'primary',
        '已处理': 'success',
        '已取消': 'info'
      },
      
      // 出库类型文本映射
      outboundTypeTextMap: {
        '': '全部',
        '1': '一件代发',
        '2': '备货中转'
      },
      
      // 处理对话框
      processDialog: {
        visible: false,
        form: {
          processType: '',
          remark: ''
        }
      },
      
      // 处理方式选项
      processTypeOptions: [
        { value: 'resolve', label: '标记已解决' },
        { value: 'return', label: '退回处理' },
        { value: 'cancel', label: '取消订单' },
        { value: 'reassign', label: '重新分配' }
      ]
    }
  },
  computed: {
    // 可见的详情页操作按钮
    visibleDetailActions() {
      return this.detailActions.filter(action => action.visible(this.abnormalDetail))
    }
  },
  async mounted() {
    await this.fetchAbnormalDetail()
  },
  methods: {
    // 获取异常件详情
    async fetchAbnormalDetail() {
      if (!this.abnormalId) {
        ElMessage.error('异常件ID不能为空')
        return
      }
      
      this.loading = true
      
      try {
        const response = await getAbnormalDetail(this.abnormalId)
        
        if (response && response.code === 200) {
          this.abnormalDetail = response.data || {}
        }
      } catch (error) {
        console.error('获取异常件详情失败', error)
        ElMessage.error('获取异常件详情失败')
      } finally {
        this.loading = false
      }
    },
    
    // 复制条码
    async copyBarcode() {
      try {
        await navigator.clipboard.writeText(this.abnormalDetail.barcode)
        ElMessage.success('复制成功')
      } catch (error) {
        console.error('复制失败', error)
        ElMessage.error('复制失败')
      }
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      return this.statusTagTypeMap[status] || 'info'
    },
    
    // 获取出库类型文本
    getOutboundTypeText(type) {
      return this.outboundTypeTextMap[type] || type
    },
    
    // 处理操作按钮点击
    handleAction(action) {
      switch (action) {
        case 'process':
          this.handleProcess()
          break
        case 'edit':
          this.handleEdit()
          break
        case 'delete':
          this.handleDelete()
          break
        default:
          console.warn(`未知的操作: ${action}`)
      }
    },
    
    // 处理异常件
    handleProcess() {
      this.processDialog.visible = true
      this.processDialog.form = {
        processType: '',
        remark: ''
      }
    },
    
    // 确认处理
    async confirmProcess() {
      try {
        await this.$refs.processForm.validate()
        
        ElMessage.success('处理成功')
        this.processDialog.visible = false
        this.fetchAbnormalDetail()
      } catch (error) {
        console.error('表单验证失败', error)
      }
    },
    
    // 编辑异常件
    handleEdit() {
      ElMessage.info('编辑功能待实现')
    },
    
    // 删除异常件
    handleDelete() {
      ElMessage.info('删除功能待实现')
    }
  }
}
</script>

<style scoped>
.abnormal-detail-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 120px);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.abnormal-info-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
}

.abnormal-barcode {
  font-weight: 700;
  font-size: 18px;
}

.copy-icon {
  color: var(--el-color-primary);
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s;
}

.copy-icon:hover {
  color: var(--el-color-primary-dark-2);
}

.status-tag {
  margin-left: 4px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.basic-info-card,
.exception-info-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.card-header {
  font-weight: 600;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  min-height: 32px;
}

.info-item label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: var(--el-text-color-primary);
  word-break: break-all;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
