<script setup>
import { defineProps, defineEmits } from 'vue'
import { Warning } from '@element-plus/icons-vue'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    printData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['success', 'cancel'])


// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 确定
const submitForm = async () => {
    try {
        emit('success')
    } catch (error) {
        console.error('操作失败', error)
    }
}

</script>

<template>
    <el-dialog :model-value="visible" width="500px">
      <br>

      <div class="app-container">

        <div style="font-size: 14px; font-weight: bolder; text-align: center" >
          <span style="">
            <el-icon><Warning /></el-icon>
            订单重复打印，是否继续打印？
          </span>
        </div>

        <el-table :data="printData" style="width: 100%;margin-top: 40px;">

          <el-table-column label="出库单号" prop="sourceNo" />
          <el-table-column label="面单打印状态" prop="printStatusName" align="center"/>

        </el-table>
      </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="submitForm" >
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>