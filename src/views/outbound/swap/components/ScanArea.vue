<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Setting } from '@element-plus/icons-vue'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['scan', 'setting'])
const orderNo = ref('')

// SCAN
const handleScan = () => {
  emit('scan', orderNo.value)

  orderNo.value = ''
}

//SETTING
const showSetting = () => {
    emit('setting')
}

</script>

<template>
    <el-card class="scan-card" shadow="hover">
        <template #header>
            <div class="card-header">
                <span style="font-size: 24px;">换单</span>
            </div>
        </template>

        <div class="scan-area">
          <el-row style="font-weight: bold">
            出库单号/参考单号/平台单号
          </el-row>
          <el-row>
              <el-input
                  v-model="orderNo"
                  style="width: 280px; height: 40px; margin-top: 20px;"
                  placeholder="请扫描"
                  clearable
                  @keyup.enter="handleScan"
              />
          </el-row>

          <div  class="footer">
            <el-select disabled="" placeholder="请选择打印机" style="width: 200px;">
              <el-option key="1" value="1">请选择打印机</el-option>
            </el-select>
            <el-link style="margin-left: 10px;">
              <el-icon @click="showSetting">
                <Setting />
              </el-icon>
            </el-link>
          </div>
        </div>

    </el-card>


</template>

<style scoped>
.scan-card {
    border-radius: 8px;
    height: calc(100vh - 120px);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: none;
}

.scan-area {
  position: relative;
  height: 70vh;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: 0px;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 500;
}


</style>