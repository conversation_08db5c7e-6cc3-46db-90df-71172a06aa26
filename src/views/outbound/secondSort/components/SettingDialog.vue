<script setup>
import { defineProps, defineEmits, ref, watch } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  settingData: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['success', 'cancel'])

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 确定
const submitForm = async () => {
  try {
    // 通知父组件操作成功
    emit('success', props.settingData)
  } catch (error) {
    console.error('操作失败', error)
  }
}

// 监听打印发货清单开关，未勾选时清空下拉框
watch(
  () => props.settingData.print_shipping_list_enabled,
  function handlePrintShippingListEnabledChange(newVal) {
    if (newVal !== '1') {
      props.settingData.print_shipping_list = ''
    }
  }
)

// 监听打印发货清单下拉选择，有有效值(1或2)时自动勾选启用
watch(
  () => props.settingData.print_shipping_list,
  function handlePrintShippingListChange(newVal) {
    if (newVal === '1' || newVal === '2') {
      props.settingData.print_shipping_list_enabled = '1'
    } else {
      props.settingData.print_shipping_list_enabled = '0'
    }
  }
)

// 监听打印物流面单开关，未勾选时清空下拉框
watch(
  () => props.settingData.print_express_enabled,
  function handlePrintExpressEnabledChange(newVal) {
    if (newVal !== '1') {
      props.settingData.print_express = ''
    }
  }
)

// 监听打印物流面单下拉选择，有有效值(1或2)时自动勾选启用
watch(
  () => props.settingData.print_express,
  function handlePrintExpressChange(newVal) {
    if (newVal === '1' || newVal === '2') {
      props.settingData.print_express_enabled = '1'
    } else {
      props.settingData.print_express_enabled = '0'
    }
  }
)
</script>

<template>
  <el-dialog :title="title" :model-value="visible" width="400px">
    <el-form label-width="0px" :model="settingData" style="padding: 0 10px">
      <!-- 播种墙设置 -->
      <div class="setting-section">
        <div class="section-title">
          播种墙设置
          <el-tooltip
            content="自定义播种墙为虚拟播种墙，不设格子数上限；系统播种墙来自播种墙管理模块"
            placement="top"
          >
            <el-icon style="margin-left: 5px; color: #909399; cursor: help">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </div>
        <el-form-item style="margin-bottom: 15px">
          <el-radio-group v-model="settingData.seedWallType" style="display: block">
            <el-radio value="2" style="display: block; margin-bottom: 10px">系统播种墙</el-radio>
            <el-radio value="1" style="display: block">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="settingData.seedWallType == '1'"
          style="margin-left: 20px; margin-top: -5px"
        >
          <el-input-number
            v-model.number="settingData.seedWallMapNum"
            :min="1"
            controls-position="right"
            style="width: 100px; margin-right: 10px"
          />
          <el-radio-group v-model="settingData.seedWallMapType">
            <el-radio-button label="行" value="row" />
            <el-radio-button label="列" value="column" />
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 打印设置 -->
      <div class="setting-section">
        <div class="section-title">打印设置</div>

        <el-form-item style="margin-bottom: 10px">
          <el-checkbox
            label="打印发货清单"
            :true-value="'1'"
            :false-value="'0'"
            v-model="settingData.print_shipping_list_enabled"
          />
        </el-form-item>
        <el-form-item style="margin-left: 20px; margin-top: -5px">
          <el-select
            v-model="settingData.print_shipping_list"
            placeholder="请选择"
            style="width: 200px"
            :disabled="settingData.print_shipping_list_enabled !== '1'"
          >
            <el-option label="分拣时开始打印" value="1" />
            <el-option label="每单分拣完成时打印" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item style="margin-bottom: 10px">
          <el-checkbox
            label="打印物流面单"
            :true-value="'1'"
            :false-value="'0'"
            v-model="settingData.print_express_enabled"
          />
        </el-form-item>
        <el-form-item style="margin-left: 20px; margin-top: -5px">
          <el-select
            v-model="settingData.print_express"
            placeholder="请选择"
            style="width: 200px"
            :disabled="settingData.print_express_enabled !== '1'"
          >
            <el-option label="分拣开始时打印" value="1" />
            <el-option label="每单分拣完成时打印" value="2" />
          </el-select>
        </el-form-item>
      </div>

      <el-form-item style="margin-bottom: 15px">
        <el-checkbox
          label="重复打印物流面单时提醒"
          :true-value="'1'"
          :false-value="'0'"
          v-model="settingData.isPrintRepeatTip"
        />
      </el-form-item>

      <el-form-item style="margin-bottom: 15px">
        <el-checkbox
          label="扫描包材"
          :true-value="'1'"
          :false-value="'0'"
          v-model="settingData.isScanPack"
        />
      </el-form-item>

      <el-form-item
        v-if="settingData.isScanPack == '1'"
        style="margin-top: -5px; margin-bottom: 15px"
      >
        <el-checkbox
          label="二次分拣后立即自动计算重量"
          :true-value="'1'"
          :false-value="'0'"
          v-model="settingData.isAutoWeight"
        />
      </el-form-item>

      <el-form-item style="margin-bottom: 15px">
        <el-checkbox
          label="快捷出库"
          :true-value="'1'"
          :false-value="'0'"
          v-model="settingData.quickOutbound"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.setting-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.el-form-item {
  margin-bottom: 12px;
}

.el-radio {
  margin-right: 0;
  margin-bottom: 8px;
}

.el-checkbox {
  margin-bottom: 8px;
}
</style>
