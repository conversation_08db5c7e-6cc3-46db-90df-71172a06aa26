<script setup>
import { useRouter } from 'vue-router'
import { QuestionFilled, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

function goBack() {
  router.go(-1)
}

function goHome() {
  router.push('/')
}
</script>

<template>
  <div class="page-not-found">
    <div class="error-content">
      <div class="error-icon">
        <el-icon :size="80" color="#e6a23c">
          <QuestionFilled />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h2 class="error-title">页面组件不存在</h2>
        <p class="error-description">
          该页面的组件文件不存在或路径配置错误，请联系管理员检查配置。
        </p>
      </div>
      
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon class="mr-1">
            <ArrowLeft />
          </el-icon>
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.error-content {
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.error-icon {
  margin-bottom: 24px;
}

.error-title {
  font-size: 24px;
  color: #303133;
  margin: 0 0 16px 0;
  font-weight: 500;
}

.error-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.mr-1 {
  margin-right: 4px;
}

@media (max-width: 768px) {
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
