<script setup>
import { useRouter } from 'vue-router'
import { ArrowLeft, Lock } from '@element-plus/icons-vue'

const router = useRouter()

function goBack() {
  router.go(-1)
}

function goHome() {
  router.push('/')
}
</script>

<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-icon">
        <el-icon :size="120" color="#f56c6c">
          <Lock />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h1 class="error-title">403</h1>
        <h2 class="error-subtitle">无权限访问</h2>
        <p class="error-description">
          抱歉，您没有访问该页面的权限。请联系管理员获取相应权限。
        </p>
      </div>
      
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon class="mr-1">
            <ArrowLeft />
          </el-icon>
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.error-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.error-content {
  text-align: center;
  background: white;
  border-radius: 12px;
  padding: 60px 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.error-icon {
  margin-bottom: 30px;
}

.error-title {
  font-size: 72px;
  font-weight: bold;
  color: #f56c6c;
  margin: 0 0 10px 0;
  line-height: 1;
}

.error-subtitle {
  font-size: 24px;
  color: #303133;
  margin: 0 0 15px 0;
  font-weight: 500;
}

.error-description {
  color: #606266;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 40px 0;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.mr-1 {
  margin-right: 4px;
}

@media (max-width: 768px) {
  .error-content {
    padding: 40px 20px;
  }
  
  .error-title {
    font-size: 60px;
  }
  
  .error-subtitle {
    font-size: 20px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
