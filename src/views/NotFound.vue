<template>
  <div class="not-found-page flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
    <div class="container mx-auto text-center">
      <el-empty
        :image-size="280"
        description=""
        class="custom-empty"
      >
        <template #image>
          <img src="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" alt="Page Not Found" class="not-found-image" />
        </template>
        <template #description>
          <div class="not-found-content">
            <h1 class="text-5xl font-bold text-gray-800 dark:text-gray-100 mb-4">404</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 mb-2">哎呀！页面丢失了</p>
            <p class="text-md text-gray-500 dark:text-gray-500 mb-8">您所访问的页面似乎已经遨游到宇宙深处去了。</p>
            <el-button type="primary" size="large" @click="goHome" class="home-button">返回首页</el-button>
          </div>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

function goHome() {
  router.push('/');
}
</script>

<style scoped>
.not-found-page {
  transition: background-color 0.5s ease;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.container {
  max-width: 600px;
  padding: 40px 20px;
  background-color: var(--el-bg-color, #ffffff);
  border-radius: var(--card-radius, 12px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08), 0 5px 10px rgba(0, 0, 0, 0.04);
  transform: translateY(-20px);
  animation: floatUp 0.8s ease-out forwards;
}

.dark-mode .container {
  background-color: var(--el-bg-color-page, #1f2937); /* Slightly lighter than page background for contrast */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.15);
}

@keyframes floatUp {
  from {
    opacity: 0;
    transform: translateY(0px);
  }
  to {
    opacity: 1;
    transform: translateY(-20px);
  }
}

.not-found-image {
  width: 280px;
  max-width: 100%;
  margin-bottom: 20px;
  animation: subtleBob 3s ease-in-out infinite;
}

@keyframes subtleBob {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

.not-found-content h1 {
  line-height: 1.2;
}

.not-found-content p {
  line-height: 1.6;
}

.home-button {
  min-width: 160px;
  font-weight: 500;
  --el-button-bg-color: var(--el-color-primary);
  --el-button-border-color: var(--el-color-primary);
  --el-button-hover-bg-color: var(--el-color-primary-light-3, #409eff); 
  --el-button-hover-border-color: var(--el-color-primary-light-3, #409eff);
  transition: all 0.3s ease;
}

.home-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 113, 227, 0.2);
}

/* Overriding el-empty default styles if needed */
.custom-empty ::v-deep(.el-empty__description) {
  margin-top: 0; /* Reset if we handle description ourselves */
}

.custom-empty ::v-deep(.el-empty__bottom) {
  margin-top: 24px;
}

/* Tailwind-like utility classes (applied directly in template for simplicity here) */
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.min-h-screen { min-height: 100vh; }
.bg-gray-100 { background-color: #f7fafc; }
.dark .dark\:bg-gray-900 { background-color: #1a202c; }
.p-4 { padding: 1rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.text-center { text-align: center; }
.text-5xl { font-size: 3rem; line-height: 1; }
.font-bold { font-weight: 700; }
.text-gray-800 { color: #2d3748; }
.dark .dark\:text-gray-100 { color: #f7fafc; }
.mb-4 { margin-bottom: 1rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-gray-600 { color: #4a5568; }
.dark .dark\:text-gray-400 { color: #a0aec0; }
.mb-2 { margin-bottom: 0.5rem; }
.text-md { font-size: 1rem; line-height: 1.5rem; }
.text-gray-500 { color: #718096; }
.dark .dark\:text-gray-500 { color: #a0aec0; } /* Adjusted for better dark mode readability */
.mb-8 { margin-bottom: 2rem; }

/* Dark mode specific text for the container */
.dark-mode .container .text-gray-800 { color: var(--el-text-color-primary, #f7fafc);}
.dark-mode .container .text-gray-600 { color: var(--el-text-color-regular, #a0aec0);}
.dark-mode .container .text-gray-500 { color: var(--el-text-color-secondary, #718096);}
</style>
