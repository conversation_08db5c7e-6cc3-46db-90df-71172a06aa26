<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// ElMessage 已通过 auto-import 自动导入
import { User, Lock, Key } from '@element-plus/icons-vue'
import { useUserStore } from '@/store'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单数据
const loginForm = reactive({
  email: '',
  password: '',
  remember: false,
})

// 表单验证规则
const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应在6到20个字符之间', trigger: 'blur' },
  ],
}

// 使用userStore中的loading状态
const loading = computed(() => userStore.isLoading)

// 表单引用
const formRef = ref(null)

// 登录处理函数
const handleLogin = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    const success = await userStore.loginAction({
      email: loginForm.email,
      password: loginForm.password,
    })

    if (success) {
      if (loginForm.remember) {
        // 如果选择了"记住我"，保存用户名
        localStorage.setItem('remember_username', loginForm.email)
      } else {
        localStorage.removeItem('remember_username')
      }

      // 登录成功后跳转到重定向页面或默认页面
      const redirectPath = route.query.redirect || '/'
      router.replace(redirectPath)
    }
  } catch (error) {
    console.error('登录过程中发生错误:', error)
    ElMessage.error('登录失败，请检查网络连接后重试')
  }
}

// 初始化
onMounted(() => {
  // 如果有记住的用户名，自动填充
  const rememberedUsername = localStorage.getItem('remember_username')
  if (rememberedUsername) {
    loginForm.email = rememberedUsername
    loginForm.remember = true
  }

  // 如果已登录，直接跳转到首页或重定向页面
  if (userStore.isLoggedIn) {
    const redirectPath = route.query.redirect || '/'
    router.replace(redirectPath)
  }
})
</script>

<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧动效区域 -->
      <div class="login-left">
        <div class="brand-logo">
          <el-icon class="logo-icon"><Key /></el-icon>
          <h1 class="brand-name">Wms Pro</h1>
        </div>
        <div class="login-features">
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon><Key /></el-icon>
            </div>
            <div class="feature-text">
              <h3>安全可靠</h3>
              <p>基于最新技术栈，提供安全可靠的管理体验</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="feature-text">
              <h3>优雅界面</h3>
              <p>精心设计的风格界面，简洁优雅</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-right">
        <div class="login-card">
          <div class="login-header">
            <h2 class="welcome-title">欢迎回来</h2>
            <p class="welcome-subtitle">请使用您的邮箱账号登录</p>
          </div>

          <el-form
            ref="formRef"
            :model="loginForm"
            :rules="rules"
            class="login-form"
            label-position="top"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="email" label="邮箱">
              <el-input
                v-model="loginForm.email"
                placeholder="请输入邮箱"
                size="large"
                autocomplete="email"
                class="custom-input"
              >
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="password" label="密码">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                autocomplete="current-password"
                show-password
                class="custom-input"
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <div class="form-actions">
              <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
              <el-link type="primary" class="forgot-link">忘记密码？</el-link>
            </div>

            <el-button
              type="primary"
              :loading="loading"
              class="login-button"
              @click="handleLogin"
              size="large"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>

            <div class="register-link">
              还没有账号？ <el-link type="primary" class="register-text">立即注册</el-link>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.login-wrapper {
  display: flex;
  width: 100%;
  max-width: 1000px;
  height: 600px;
  background-color: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
}

/* 左侧区域样式 */
.login-left {
  flex: 1;
  background: linear-gradient(135deg, #f5f7fa 0%, #0071e3 100%);
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
  position: relative;
  overflow: hidden;
}

.login-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.brand-logo {
  display: flex;
  align-items: center;
  z-index: 2;
}

.logo-icon {
  font-size: 32px;
  margin-right: 10px;
}

.brand-name {
  font-size: 28px;
  font-weight: 500;
}

.login-features {
  z-index: 2;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.feature-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.feature-text h3 {
  font-weight: 500;
  margin-bottom: 5px;
  font-size: 18px;
}

.feature-text p {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}

/* 右侧登录表单区域 */
.login-right {
  flex: 1;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
}

.login-card {
  width: 100%;
  max-width: 380px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.welcome-title {
  font-size: 28px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.welcome-subtitle {
  color: #666;
  font-size: 16px;
}

.login-form {
  margin-bottom: 20px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-link {
  font-size: 14px;
  font-weight: 500;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 10px;
  margin-bottom: 16px;
  transition: all 0.3s;
}

.login-button:hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.register-text {
  font-weight: 500;
}

/* 自定义输入框样式 */
.custom-input {
  height: 48px;
}

/* Element Plus 组件样式优化 */
:deep(.el-form-item) {
  width: 100% !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 10px;
}

:deep(.el-input__inner) {
  color: #333;
  font-weight: 400;
}

:deep(.el-input__inner::placeholder) {
  color: #999;
}

:deep(.el-checkbox__label) {
  color: #666;
  font-weight: 400;
}
</style>
