import { ref } from 'vue'

const isDark = ref(false)

export function useDarkMode() {
  const toggleDark = () => {
    isDark.value = !isDark.value
    document.documentElement.classList.toggle('dark', isDark.value)
    localStorage.setItem('vue-dark-mode', isDark.value.toString())
  }
  
  const initDarkMode = () => {
    const saved = localStorage.getItem('vue-dark-mode')
    if (saved !== null) {
      isDark.value = saved === 'true'
    } else {
      // 检测系统偏好
      isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    document.documentElement.classList.toggle('dark', isDark.value)
  }
  
  const setDark = (value) => {
    isDark.value = value
    document.documentElement.classList.toggle('dark', isDark.value)
    localStorage.setItem('vue-dark-mode', isDark.value.toString())
  }
  
  return {
    isDark,
    toggleDark,
    initDarkMode,
    setDark
  }
} 