<script setup>
import { ref, computed, watch } from 'vue'

// 禁用属性继承，避免属性传递给子组件
defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ type: '', value: null }),
  },
  typeOptions: {
    type: Array,
    default: () => [
      { value: 'createTime', label: '创建时间', dateType: 'datetime' },
      { value: 'updateTime', label: '更新时间', dateType: 'datetime' },
      { value: 'dateRange', label: '日期范围', dateType: 'daterange' },
      { value: 'datetimeRange', label: '时间范围', dateType: 'datetimerange' },
    ],
  },
  placeholder: {
    type: String,
    default: '请选择时间',
  },
  size: {
    type: String,
    default: 'default',
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

// 内部状态 - 默认使用第一个类型选项
const selectedType = ref(props.modelValue.type || props.typeOptions[0]?.value || '')
const selectedValue = ref(props.modelValue.value || null)

// 如果没有传入类型但有默认类型，初始化时设置默认类型
if (!props.modelValue.type && props.typeOptions[0]?.value) {
  selectedType.value = props.typeOptions[0].value
}

// 当前选中类型的配置
const currentTypeConfig = computed(() => {
  return props.typeOptions.find((option) => option.value === selectedType.value) || {}
})

// 当前日期选择器的类型
const currentDateType = computed(() => {
  return currentTypeConfig.value.dateType || 'datetime'
})

// 当前日期选择器的占位符
const currentPlaceholder = computed(() => {
  if (!selectedType.value) return props.placeholder

  const config = currentTypeConfig.value
  const isRange = config.dateType?.includes('range')

  if (isRange) {
    return ['开始时间', '结束时间']
  }

  return `请选择${config.label}`
})

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectedType.value = newVal.type || ''
    selectedValue.value = newVal.value || null
  },
  { deep: true },
)

// 监听类型变化，清空值
watch(selectedType, (newType) => {
  if (newType !== props.modelValue.type) {
    selectedValue.value = null
    updateValue()
  }
})

// 监听值变化
watch(
  selectedValue,
  () => {
    updateValue()
  },
  { deep: true },
)

// 更新绑定值
const updateValue = () => {
  const newValue = {
    type: selectedType.value,
    value: selectedValue.value,
  }

  emit('update:modelValue', newValue)
  emit('change', newValue)
}

// 类型选择变化
const handleTypeChange = (type) => {
  selectedType.value = type
}

// 日期值变化
const handleDateChange = (value) => {
  selectedValue.value = value
}

// 日期清空
const handleDateClear = () => {
  selectedValue.value = null
}

// 暴露方法给父组件
defineExpose({
  focus: () => {
    if (datePickerRef.value) {
      datePickerRef.value.focus()
    }
  },
  blur: () => {
    if (datePickerRef.value) {
      datePickerRef.value.blur()
    }
  },
})
</script>

<template>
  <div class="composite-date-picker">
    <div class="picker-group">
      <!-- 类型选择下拉框 -->
      <el-select
        v-model="selectedType"
        placeholder="类型"
        :size="size"
        :disabled="disabled"
        class="type-select"
        @change="handleTypeChange"
      >
        <el-option
          v-for="option in typeOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>

      <!-- 日期选择器 -->
      <el-date-picker
        v-model="selectedValue"
        :type="currentDateType"
        :placeholder="
          Array.isArray(currentPlaceholder) ? currentPlaceholder[0] : currentPlaceholder
        "
        :start-placeholder="Array.isArray(currentPlaceholder) ? currentPlaceholder[0] : ''"
        :end-placeholder="Array.isArray(currentPlaceholder) ? currentPlaceholder[1] : ''"
        :size="size"
        :disabled="disabled"
        :clearable="clearable"
        class="date-picker"
        @change="handleDateChange"
        @clear="handleDateClear"
      />
    </div>
  </div>
</template>

<style scoped>
.composite-date-picker {
  width: 100%;
}

.picker-group {
  display: flex;
  width: 100%;
}

.type-select {
  width: 120px;
  flex-shrink: 0;
}

.date-picker {
  flex: 1;
  min-width: 0;
}

.placeholder-input {
  flex: 1;
  min-width: 0;
}

.date-input {
  width: 100%;
}

/* 让选择器和日期选择器看起来像一个整体 */
:deep(.type-select .el-input__wrapper) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  background-color: var(--el-fill-color-light);
}

:deep(.date-picker .el-input__wrapper) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

:deep(.placeholder-input .el-input__wrapper) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* 聚焦状态处理 */
:deep(.type-select.is-focus .el-input__wrapper) {
  border-right: 1px solid var(--el-color-primary);
}

:deep(.picker-group:focus-within .type-select .el-input__wrapper) {
  border-right: none;
}
</style>
