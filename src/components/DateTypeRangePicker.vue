<script setup>
import { ref, computed, defineEmits } from 'vue'

// Props定义
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  // 选中的日期类型值
  dateType: {
    type: String,
    required: true
  },
  // 选中的日期范围
  dateRange: {
    type: Array,
    default: () => []
  },
  // 日期类型选项
  dateTypeOptions: {
    type: Array,
    default: () => []
  },
  // 日期选择器占位符
  startPlaceholder: {
    type: String,
    default: '开始日期'
  },
  endPlaceholder: {
    type: String,
    default: '结束日期'
  },
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD'
  }
})

// 更新日期类型
const handleDateTypeChange = (value) => {
  emit('update:dateType', value)
}

// 更新日期范围
const handleDateRangeChange = (value) => {
  emit('update:dateRange', value)
}

// Emits定义
const emit = defineEmits(['update:dateType', 'update:dateRange'])

// 快捷选项
const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]
</script>

<template>
  <div class="datetime-container">
    <el-select
        :model-value="dateType"
        @update:modelValue="handleDateTypeChange"
        class="date-select"
    >
      <el-option
          v-for="option in dateTypeOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
      />
    </el-select>
    <el-date-picker
        :model-value="dateRange"
        @update:modelValue="handleDateRangeChange"
        type="daterange"
        range-separator="至"
        :start-placeholder="startPlaceholder"
        :end-placeholder="endPlaceholder"
        :value-format="valueFormat"
        class="date-picker"
        :shortcuts="shortcuts"
    />
  </div>

</template>

<style scoped>
/* 样式可以根据需要添加 */

/* 自定义日期选择器样式 */
.datetime-container {
  display: flex;
  width: 350px;
  position: relative;
}

.date-select {
  width: 110px;
  z-index: 3;
}

.date-select :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  padding-left: 12px;
  border-radius: 4px 0 0 4px;
  background-color: var(--el-fill-color-blank);
}

.date-select:focus-within :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  z-index: 2;
}

.date-picker {
  flex: 1;
}

.date-picker :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  border-radius: 0 4px 4px 0;
  margin-left: -1px;
}

/* 隐藏日期选择器图标 */
.date-picker :deep(.el-input__prefix-inner) {
  display: none;
}

.date-picker:focus-within :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

/* 悬停和聚焦效果 */
.datetime-container:hover .date-select :deep(.el-input__wrapper),
.datetime-container:hover .date-picker :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
}
</style>
