<script setup>
// 1. 导入
import { Grid } from '@element-plus/icons-vue'

// 2. Props定义
defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})
</script>

<template>
  <div class="logo-container">
    <div class="logo-wrapper">
      <el-icon class="logo-icon">
        <Grid />
      </el-icon>
      <h1 v-if="!isCollapsed" class="logo-title">Wms Panel</h1>
    </div>
  </div>
</template>

<style scoped>
.logo-container {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  overflow: hidden;
  transition: all 0.3s;
  border-bottom: 1px solid var(--el-border-color);
}

.logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  width: 100%;
}

.logo-icon {
  font-size: 20px;
  color: var(--el-color-primary);
  flex-shrink: 0;
  transition: margin 0.3s;
}

/* 当有标题时，图标有右边距 */
.logo-wrapper:not(:has(.logo-title)) .logo-icon {
  margin-right: 0;
}

.logo-wrapper:has(.logo-title) .logo-icon {
  margin-right: 8px;
}

.logo-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  transition: opacity 0.3s;
}
</style> 