<script setup>
// 1. 导入
import { useLayoutStore } from '@/store/modules/layout'
import AppLogo from './AppLogo.vue'
import AppMenu from './AppMenu.vue'
import {
  Fold,
  Expand
} from '@element-plus/icons-vue'

// 2. Props定义
defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

// 3. Emits定义
const emit = defineEmits(['menu-select', 'toggle-sidebar'])

// 4. 响应式状态
const layoutStore = useLayoutStore()

// 5. 方法
function handleMenuSelect(index) {
  emit('menu-select', index)
}

function handleToggleSidebar() {
  emit('toggle-sidebar')
}
</script>

<template>
  <div
    class="sidebar"
    :class="{
      'is-collapsed': isCollapsed,
      'sidebar-dark': layoutStore.sidebarConfig.theme === 'dark'
    }"
    :style="{
      width: isCollapsed ? '64px' : '200px'
    }"
  >
    <AppLogo :is-collapsed="isCollapsed" />
    <AppMenu
      :is-collapsed="isCollapsed"
      :is-dark="layoutStore.sidebarConfig.theme === 'dark'"
      @menu-select="handleMenuSelect"
    />
    
    <!-- 折叠按钮（简化：始终显示） -->
    <div class="sidebar-footer">
      <el-button
        link
        @click="handleToggleSidebar"
        class="toggle-button"
      >
        <el-icon class="toggle-icon">
          <Fold v-if="!isCollapsed" />
          <Expand v-else />
        </el-icon>
        <span v-if="!isCollapsed" class="toggle-text">收起</span>
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.sidebar {
  height: 100%;
  background-color: var(--el-bg-color);
  transition: all var(--layout-transition-duration);
  z-index: 100;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  padding: 0;
  flex-shrink: 0;
}

/* 深色主题 - 保持与浅色模式类似，只修改背景色 */
.sidebar-dark {
  background-color: var(--el-bg-color-overlay);
}

/* 浮动模式 */
.sidebar-float {
  position: fixed;
  top: var(--layout-header-height);
  left: 0;
  height: calc(100vh - var(--layout-header-height));
  z-index: 1000;
  border-radius: 0 var(--layout-border-radius) var(--layout-border-radius) 0;
}

/* 折叠状态样式现在由AppMenu.vue中的CSS控制 */

/* 侧边栏底部折叠按钮 */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px;
  border-top: 1px solid var(--el-border-color);
  background-color: inherit;
}

.toggle-button {
  width: 100%;
  font-size: 12px;
  color: var(--el-text-color-primary);
  background: none;
  border: none;
  padding: 6px 12px;
  border-radius: var(--layout-border-radius);
  transition: all var(--layout-transition-duration);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-button:hover {
  background-color: var(--el-fill-color-light);
}

.toggle-icon {
  font-size: 14px;
  transition: transform var(--layout-transition-duration);
  margin-right: 6px;
}

.sidebar.is-collapsed .toggle-icon {
  margin-right: 0;
}

.toggle-text {
  font-size: 11px;
}


</style>