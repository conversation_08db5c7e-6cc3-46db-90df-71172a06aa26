<script setup>
// 1. 导入
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { usePermissionStore } from '@/store/modules/permission'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 2. Props定义
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  },
  isDark: {
    type: Boolean,
    default: false
  }
})

// 3. Emits定义
const emit = defineEmits(['menu-select'])

// 4. 响应式状态
const route = useRoute()
const permissionStore = usePermissionStore()

// 5. 计算属性
const currentPath = computed(() => route.path)

// 获取动态菜单数据
const menuItems = computed(() => {
  // console.log('菜单组件 - 权限store状态:', {
  //   hasMenus: permissionStore.hasMenus,
  //   menusLength: permissionStore.menus.length,
  //   visibleMenusLength: permissionStore.visibleMenus.length,
  //   menus: permissionStore.menus,
  //   visibleMenus: permissionStore.visibleMenus
  // })
  return permissionStore.visibleMenus
})

const defaultActive = computed(() => {
  // 根据当前路由路径确定激活的菜单项
  return currentPath.value
})

const defaultOpeneds = computed(() => {
  if (props.isCollapsed) return []

  // 根据当前路径确定需要展开的菜单
  const openedMenus = []
  const findParentMenus = (menus, targetPath, parentPath = '') => {
    for (const menu of menus) {
      const menuPath = parentPath ? `${parentPath}/${menu.path}` : menu.path

      if (targetPath.startsWith(menuPath) && menu.children?.length > 0) {
        openedMenus.push(menu.path)
        findParentMenus(menu.children, targetPath, menuPath)
      }
    }
  }

  findParentMenus(menuItems.value, currentPath.value)
  return openedMenus
})

// 6. 方法
function handleMenuSelect(index) {
  emit('menu-select', index)
}

// 获取图标组件
function getIconComponent(iconName) {
  if (!iconName) return null
  return ElementPlusIconsVue[iconName] || null
}
</script>

<template>
  <el-scrollbar class="menu-scrollbar">
    <el-menu
      :default-active="defaultActive"
      :default-openeds="defaultOpeneds"
      :collapse="isCollapsed"
      :unique-opened="false"
      class="sidebar-menu"
      :class="{ 'menu-dark': isDark }"
      @select="handleMenuSelect"
    >
      <template v-for="item in menuItems" :key="item.id">
        <!-- 没有子菜单的项目 -->
        <el-menu-item
          v-if="!item.children || item.children.length === 0"
          :index="item.path"
        >
          <el-icon v-if="item.icon">
            <component :is="getIconComponent(item.icon)" />
          </el-icon>
          <span>{{ item.name }}</span>
        </el-menu-item>

        <!-- 有子菜单的项目 -->
        <el-sub-menu v-else :index="item.path">
          <template #title>
            <el-icon v-if="item.icon">
              <component :is="getIconComponent(item.icon)" />
            </el-icon>
            <span>{{ item.name }}</span>
          </template>

          <template v-for="child in item.children" :key="child.id">
            <!-- 子菜单项 -->
            <el-menu-item
              v-if="!child.children || child.children.length === 0"
              :index="child.path"
            >
              <el-icon v-if="child.icon">
                <component :is="getIconComponent(child.icon)" />
              </el-icon>
              <span>{{ child.name }}</span>
            </el-menu-item>

            <!-- 三级菜单 -->
            <el-sub-menu v-else :index="child.path">
              <template #title>
                <el-icon v-if="child.icon">
                  <component :is="getIconComponent(child.icon)" />
                </el-icon>
                <span>{{ child.name }}</span>
              </template>

              <el-menu-item
                v-for="grandChild in child.children"
                :key="grandChild.id"
                :index="grandChild.path"
              >
                <el-icon v-if="grandChild.icon">
                  <component :is="getIconComponent(grandChild.icon)" />
                </el-icon>
                <span>{{ grandChild.name }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-sub-menu>
      </template>
    </el-menu>
  </el-scrollbar>
</template>

<style scoped>
.menu-scrollbar {
  height: calc(100% - 48px - 60px);
  border-right: none;
}

.menu-scrollbar :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

.sidebar-menu {
  --el-menu-item-height: auto;
  --el-menu-sub-item-height: auto;
  --el-menu-bg-color: transparent;
  --el-menu-text-color: var(--el-text-color-regular);
  --el-menu-hover-text-color: var(--el-text-color-primary);
  --el-menu-hover-bg-color: var(--el-fill-color-light);
  --el-menu-active-color: var(--el-color-primary);
  --el-menu-active-bg-color: var(--el-color-primary-light-9);

  border: none;
  padding: 12px;
}

/* 菜单项基础样式 */
.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  padding: 10px 12px;
  margin: 2px 0;
  border-radius: var(--el-border-radius-base);
  transition: all 0.3s;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  font-weight: 500;
}

/* 子菜单样式 */
.sidebar-menu :deep(.el-menu) {
  background: transparent;
  padding-left: 16px;
  margin-top: 2px;
}

.sidebar-menu :deep(.el-menu .el-menu-item) {
  padding: 8px 12px;
  margin: 1px 0;
  font-size: 13px;
}

/* 收缩状态样式 */
.sidebar-menu.el-menu--collapse {
  padding: 12px 4px;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item),
.sidebar-menu.el-menu--collapse :deep(.el-sub-menu__title) {
  width: 48px;
  height: 40px;
  margin: 2px auto;
  padding: 0 !important;
  justify-content: center;
}

/* 深色主题 - 只为选中菜单项添加卡片效果 */
.sidebar-menu.menu-dark :deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary);
  color: var(--el-color-white) !important;
  border-radius: 6px;
}

.sidebar-menu.menu-dark :deep(.el-menu .el-menu-item.is-active) {
  background-color: var(--el-color-primary);
  color: var(--el-color-white) !important;
  border-radius: 4px;
}

.sidebar-menu.menu-dark :deep(.el-menu-item.is-active span),
.sidebar-menu.menu-dark :deep(.el-menu .el-menu-item.is-active span) {
  color: var(--el-color-white) !important;
}
</style>