<script setup>
// 1. 导入
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useLayoutStore } from '@/store/modules/layout'
import { usePermissionStore } from '@/store/modules/permission'
import AppMain from './AppMain.vue'
import AppTabs from './AppTabs.vue'
import { Moon, Sunny, Setting, Grid } from '@element-plus/icons-vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { useDarkMode } from '@/composables/useDarkMode'
import AppUserDropdown from './header/AppUserDropdown.vue'

// 2. Emits定义
const emit = defineEmits(['menu-select'])

// 3. 响应式状态
const route = useRoute()
const layoutStore = useLayoutStore()
const permissionStore = usePermissionStore()
const { isDark, toggleDark } = useDarkMode()

// 4. 计算属性
const currentPath = computed(() => route.path)

// 获取动态菜单数据
const menuItems = computed(() => permissionStore.visibleMenus)

const defaultActive = computed(() => {
  // 根据当前路由路径确定激活的菜单项
  return currentPath.value
})

// 5. 方法
function handleMenuSelect(index) {
  emit('menu-select', index)
}

function handleToggleSettings() {
  layoutStore.toggleSettings()
}

// 获取图标组件
function getIconComponent(iconName) {
  if (!iconName) return null
  return ElementPlusIconsVue[iconName] || null
}
</script>

<template>
  <div class="top-nav-layout">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <!-- 左侧Logo和菜单 -->
      <div class="navbar-left">
        <!-- Logo -->
        <div class="logo-container">
          <el-icon class="logo-icon">
            <Grid />
          </el-icon>
          <h1 class="logo-title">Admin Panel</h1>
        </div>

        <!-- 水平菜单 -->
        <el-menu
          mode="horizontal"
          class="top-menu"
          :default-active="defaultActive"
          @select="handleMenuSelect"
        >
          <template v-for="item in menuItems" :key="item.id">
            <!-- 没有子菜单的项目 -->
            <el-menu-item
              v-if="!item.children || item.children.length === 0"
              :index="item.path"
            >
              <el-icon v-if="item.icon">
                <component :is="getIconComponent(item.icon)" />
              </el-icon>
              <span>{{ item.name }}</span>
            </el-menu-item>

            <!-- 有子菜单的项目 -->
            <el-sub-menu v-else :index="item.path">
              <template #title>
                <el-icon v-if="item.icon">
                  <component :is="getIconComponent(item.icon)" />
                </el-icon>
                <span>{{ item.name }}</span>
              </template>

              <template v-for="child in item.children" :key="child.id">
                <!-- 子菜单项 -->
                <el-menu-item
                  v-if="!child.children || child.children.length === 0"
                  :index="child.path"
                >
                  <el-icon v-if="child.icon">
                    <component :is="getIconComponent(child.icon)" />
                  </el-icon>
                  <span>{{ child.name }}</span>
                </el-menu-item>

                <!-- 三级菜单 -->
                <el-sub-menu v-else :index="child.path">
                  <template #title>
                    <el-icon v-if="child.icon">
                      <component :is="getIconComponent(child.icon)" />
                    </el-icon>
                    <span>{{ child.name }}</span>
                  </template>

                  <el-menu-item
                    v-for="grandChild in child.children"
                    :key="grandChild.id"
                    :index="grandChild.path"
                  >
                    <el-icon v-if="grandChild.icon">
                      <component :is="getIconComponent(grandChild.icon)" />
                    </el-icon>
                    <span>{{ grandChild.name }}</span>
                  </el-menu-item>
                </el-sub-menu>
              </template>
            </el-sub-menu>
          </template>
        </el-menu>
      </div>

      <!-- 右侧操作区 -->
      <div class="navbar-right">
        <!-- 暗夜模式切换 -->
        <el-button circle class="action-button" @click="toggleDark">
          <el-icon>
            <Moon v-if="!isDark" />
            <Sunny v-else />
          </el-icon>
        </el-button>

        <!-- 布局设置 -->
        <el-button
          v-if="layoutStore.headerConfig.showSettings"
          circle
          class="action-button"
          @click="handleToggleSettings"
        >
          <el-icon>
            <Setting />
          </el-icon>
        </el-button>

        <!-- 用户信息 -->
        <AppUserDropdown />
      </div>
    </div>

    <!-- 主内容区容器 -->
    <div class="main-content-wrapper">
      <!-- 标签页（始终显示） -->
      <AppTabs />

      <!-- 主内容区 -->
      <div class="app-main-wrapper">
        <AppMain />
      </div>
    </div>
  </div>
</template>

<style scoped>
.top-nav-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.top-navbar {
  background-color: var(--el-bg-color);
  box-shadow: var(--el-box-shadow-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 48px; /* 固定高度 */
  transition: all var(--layout-transition-duration);
  z-index: 100;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;
}

.logo-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.logo-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.top-menu {
  --el-menu-horizontal-height: 48px;
  --el-menu-bg-color: transparent;
  --el-menu-text-color: var(--el-text-color-regular);
  --el-menu-hover-text-color: var(--el-text-color-primary);
  --el-menu-hover-bg-color: var(--el-fill-color-light);
  --el-menu-active-color: var(--el-color-primary);
  --el-menu-active-bg-color: var(--el-color-primary-light-9);

  flex: 1;
  border-bottom: none;
}

/* 菜单项基础样式 */
.top-menu :deep(.el-menu-item),
.top-menu :deep(.el-sub-menu__title) {
  margin: 0 4px;
  border-radius: var(--layout-border-radius);
  transition: all var(--layout-transition-duration);
}

/* 子菜单弹出层样式 */
.top-menu :deep(.el-menu--popup) {
  border-radius: var(--layout-border-radius);
  padding: 4px 0;
}

.top-menu :deep(.el-menu--popup .el-menu-item) {
  height: 40px;
  padding: 8px 16px;
  margin: 2px 8px;
  border-radius: var(--layout-border-radius);
}

/* 深色主题适配 - 统一使用Element UI原生变量 */
html.dark .top-menu {
  --el-menu-text-color: var(--el-text-color-regular);
  --el-menu-hover-text-color: var(--el-text-color-primary);
  --el-menu-hover-bg-color: var(--el-fill-color);
  --el-menu-active-bg-color: var(--el-color-primary);
  --el-menu-active-color: var(--el-color-white);
}

/* 激活状态样式 */
.top-menu :deep(.el-menu-item.is-active),
.top-menu :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
  font-weight: 500;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  color: var(--el-text-color-regular);
  border-radius: 50%;
  transition: all var(--layout-transition-duration);
  width: 32px;
  height: 32px;
}

.action-button:hover {
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-light);
}

/* 主内容区容器 */
.main-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all var(--layout-transition-duration);
}

/* AppMain包装器 */
.app-main-wrapper {
  flex: 1;
  overflow: hidden;
}

.app-main-wrapper :deep(.app-main) {
  margin-top: 0 !important;
  height: 100%;
}


</style>
