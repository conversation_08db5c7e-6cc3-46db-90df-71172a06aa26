<script setup>
// 1. 导入
import { computed } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'
import {
  LAYOUT_MODE_OPTIONS,
  SIDEBAR_THEME_OPTIONS,
  THEME_COLORS
} from '@/constants/layout'
import {
  Setting,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 2. Props定义
defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// 3. Emits定义
const emit = defineEmits(['update:visible'])

// 4. 响应式状态
const layoutStore = useLayoutStore()

// 5. 计算属性
const drawerVisible = computed({
  get: () => layoutStore.isSettingsVisible,
  set: (value) => {
    layoutStore.setSettingsVisible(value)
    emit('update:visible', value)
  }
})

// 6. 方法

/**
 * 处理布局模式变化
 */
function handleLayoutModeChange(mode) {
  layoutStore.updateLayoutMode(mode)
  ElMessage.success(`已切换到${LAYOUT_MODE_OPTIONS.find(item => item.value === mode)?.label}`)
}



/**
 * 处理主题颜色变化
 */
function handlePrimaryColorChange(color) {
  layoutStore.updateThemeConfig({ primaryColor: color })
}

/**
 * 重置为默认配置
 */
async function handleReset() {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有配置为默认值吗？此操作不可撤销。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    layoutStore.resetToDefault()
    ElMessage.success('已重置为默认配置')
  } catch {
    // 用户取消
  }
}
</script>

<template>
  <el-drawer
    v-model="drawerVisible"
    title="布局设置"
    direction="rtl"
    size="360px"
    class="layout-settings-drawer"
  >
    <template #header="{ titleId, titleClass }">
      <div class="settings-header">
        <h4 :id="titleId" :class="titleClass">
          <el-icon><Setting /></el-icon>
          布局设置
        </h4>
        <div class="header-actions">
          <el-tooltip content="重置配置">
            <el-button
              circle
              size="small"
              @click="handleReset"
              :icon="Refresh"
            />
          </el-tooltip>
        </div>
      </div>
    </template>

    <div class="settings-content">
      <div class="settings-section">
        <h5 class="section-title">布局模式</h5>
        <div class="layout-mode-grid">
          <div
            v-for="mode in LAYOUT_MODE_OPTIONS"
            :key="mode.value"
            class="layout-mode-item"
            :class="{ active: layoutStore.currentLayoutMode === mode.value }"
            @click="handleLayoutModeChange(mode.value)"
          >
            <div class="mode-icon">
              <el-icon>
                <component :is="mode.icon" />
              </el-icon>
            </div>
            <div class="mode-label">{{ mode.label }}</div>
            <div class="mode-description">{{ mode.description }}</div>
          </div>
        </div>
      </div>

      <div v-if="layoutStore.showSidebar" class="settings-section">
        <h5 class="section-title">侧边栏</h5>

        <div class="setting-item">
          <label>主题</label>
          <el-radio-group
            :model-value="layoutStore.sidebarConfig.theme"
            @change="(val) => layoutStore.updateSidebarConfig({ theme: val })"
          >
            <el-radio
              v-for="option in SIDEBAR_THEME_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 主题颜色配置（简化版） -->
      <div class="settings-section">
        <h5 class="section-title">主题颜色</h5>

        <div class="theme-colors-grid">
          <div
            v-for="color in THEME_COLORS"
            :key="color.name"
            class="theme-color-item"
            :class="{ active: layoutStore.themeConfig.primaryColor === color.color }"
            @click="handlePrimaryColorChange(color.color)"
          >
            <div
              class="color-block"
              :style="{ backgroundColor: color.color }"
            ></div>
            <span class="color-name">{{ color.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<style scoped>
.layout-settings-drawer {
  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--el-border-color);
}

.settings-header h4 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.settings-content {
  padding: 16px 24px;
  height: calc(100% - 80px);
  overflow-y: auto;
}

.settings-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
}

/* 布局模式网格 */
.layout-mode-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.layout-mode-item {
  padding: 12px;
  border: 2px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.layout-mode-item:hover {
  border-color: var(--el-color-primary-light-3);
  background-color: var(--el-color-primary-light-9);
}

.layout-mode-item.active {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.mode-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: var(--el-color-primary);
}

.mode-label {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.mode-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.setting-item label {
  font-size: 14px;
  color: var(--el-text-color-primary);
  min-width: 80px;
}

.setting-item .el-select,
.setting-item .el-radio-group,
.setting-item .el-switch {
  flex: 1;
  max-width: 200px;
}

.setting-note {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background-color: var(--el-fill-color-light);
  padding: 4px 8px;
  border-radius: 4px;
}

.theme-colors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.theme-color-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  cursor: pointer;
  transition: all 0.3s;
}

.theme-color-item:hover {
  border-color: var(--el-color-primary-light-3);
  background-color: var(--el-fill-color-light);
}

.theme-color-item.active {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.color-block {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

.color-name {
  font-size: 12px;
  flex: 1;
}


</style> 