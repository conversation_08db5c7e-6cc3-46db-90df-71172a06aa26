<script setup>
// 1. 导入
// (无需导入)

// 2. Pro<PERSON> & Emits 定义
// (此组件无需props和emits)

// 3. 响应式状态
// (无需状态)

// 4. 方法
function getTransitionName() {
  // 简化：使用固定的过渡动画
  return 'fade-transform'
}
</script>

<template>
  <div class="app-main">
    <router-view v-slot="{ Component }">
      <transition 
        :name="getTransitionName()" 
        mode="out-in"
      >
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<style scoped>
.app-main {
  flex: 1;
  overflow-y: auto;
  background-color: var(--el-bg-color-page);
  min-height: 0;
  transition: all var(--layout-transition-duration);
  padding: 16px; /* 添加默认内边距 */
}

/* 淡入淡出动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all var(--layout-transition-duration);
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 滑动动画 */
.slide-transform-enter-active,
.slide-transform-leave-active {
  transition: all var(--layout-transition-duration);
}

.slide-transform-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-transform-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

/* 缩放动画 */
.zoom-transform-enter-active,
.zoom-transform-leave-active {
  transition: all var(--layout-transition-duration);
}

.zoom-transform-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.zoom-transform-leave-to {
  opacity: 0;
  transform: scale(1.05);
}
</style> 