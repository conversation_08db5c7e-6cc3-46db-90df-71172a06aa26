<script setup>
// 1. 导入
import { useTabsStore } from '@/store/modules/tabs'
import { useRouter, useRoute } from 'vue-router'
import { watch, onMounted } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'
import { useDarkMode } from '@/composables/useDarkMode'
import { LAYOUT_MODES } from '@/constants/layout'
import { Close, Moon, Sunny, Setting, MoreFilled } from '@element-plus/icons-vue'
import AppUserDropdown from './header/AppUserDropdown.vue'

// 2. Props & Emits 定义
// (此组件无需props和emits)

// 3. 响应式状态
const tabsStore = useTabsStore()
const router = useRouter()
const route = useRoute()
const layoutStore = useLayoutStore()
const { isDark, toggleDark } = useDarkMode()

// 4. 方法
function handleTabClick(tab) {
  const targetRoute = tabsStore.switchTab(tab.name)
  if (targetRoute) {
    // 使用完整的路由信息进行跳转，包含查询参数
    router.push({
      path: targetRoute.path,
      query: targetRoute.query || {},
      params: targetRoute.params || {}
    })
  }
}

function handleTabRemove(tabName) {
  const nextTab = tabsStore.removeTab(tabName)
  if (nextTab && nextTab.path !== route.path) {
    router.push(nextTab.path)
  }
}

function handleTabContextMenu(event) {
  event.preventDefault()
}

function handleToggleSettings() {
  layoutStore.toggleSettings()
}

// 标签页批量操作
function handleCloseOtherTabs() {
  tabsStore.closeOtherTabs(tabsStore.activeTab)
}

function handleCloseAllTabs() {
  const firstTab = tabsStore.closeAllTabs()
  if (firstTab && firstTab.path !== route.path) {
    router.push(firstTab.path)
  }
}

// 5. 生命周期钩子
onMounted(() => {
  // 确保当前路由对应的标签页存在并激活
  if (route.name && route.path !== '/login') {
    tabsStore.addTab(route)
    tabsStore.updateActiveTabByRoute(route)
  }
})

// 6. 监听路由变化，自动添加标签页
watch(
  () => route,
  (newRoute) => {
    if (newRoute.name && newRoute.path !== '/login') {
      tabsStore.addTab(newRoute)
    }
  },
  { immediate: true, deep: true },
)
</script>

<template>
  <div
    class="app-tabs"
    :class="{ 'sidebar-mode': layoutStore.currentLayoutMode === LAYOUT_MODES.SIDEBAR }"
  >
    <div class="tabs-container">
      <!-- 左侧：标签页区域 -->
      <div class="tabs-left">
        <div
          v-for="tab in tabsStore.tabs"
          :key="tab.name"
          class="tab-item"
          :class="{ active: tabsStore.activeTab === tab.name }"
          @click="handleTabClick(tab)"
          @contextmenu="handleTabContextMenu($event, tab)"
        >
          <span class="tab-title">{{ tab.title }}</span>
          <el-icon v-if="tab.closable" class="tab-close" @click.stop="handleTabRemove(tab.name)">
            <Close />
          </el-icon>
        </div>
      </div>

      <!-- 右侧：操作按钮区域 -->
      <div class="tabs-right">
        <!-- 标签页操作下拉按钮 -->
        <el-dropdown trigger="click" class="tabs-actions-dropdown">
          <el-button circle class="action-button">
            <el-icon>
              <MoreFilled />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleCloseOtherTabs">关闭其他标签</el-dropdown-item>
              <el-dropdown-item @click="handleCloseAllTabs">关闭所有标签</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 仅在侧边栏模式下显示的其他按钮 -->
        <template v-if="layoutStore.currentLayoutMode === LAYOUT_MODES.SIDEBAR">
          <!-- 暗夜模式切换 -->
          <el-button circle class="action-button" @click="toggleDark">
            <el-icon>
              <Moon v-if="!isDark" />
              <Sunny v-else />
            </el-icon>
          </el-button>

          <!-- 布局设置 -->
          <el-button
            v-if="layoutStore.headerConfig.showSettings"
            circle
            class="action-button"
            @click="handleToggleSettings"
          >
            <el-icon>
              <Setting />
            </el-icon>
          </el-button>

          <!-- 用户信息 -->
          <AppUserDropdown />
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-tabs {
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  height: 40px; /* 顶部模式下的默认高度 */
  flex-shrink: 0;
  overflow: hidden;
}

.app-tabs.sidebar-mode {
  height: 48px;
}

.tabs-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 16px;
  overflow: hidden;
}

.tabs-left {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
}

.tabs-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color);
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  min-width: 80px;
  max-width: 160px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 侧边栏模式下使用更高的标签页 */
.sidebar-mode .tab-item {
  padding: 8px 14px;
  font-size: 13px;
}

.tab-item:hover {
  background-color: var(--el-fill-color);
  color: var(--el-text-color-primary);
}

.tab-item.active {
  background-color: var(--el-bg-color-page);
  border-bottom-color: var(--el-bg-color-page);
  color: var(--el-color-primary);
  font-weight: 500;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-close {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  opacity: 0.6;
}

.tab-close:hover {
  background-color: var(--el-color-danger-light-7);
  color: var(--el-color-danger);
  opacity: 1;
}

.tab-item.active .tab-close:hover {
  background-color: var(--el-color-danger-light-8);
}

/* 操作按钮样式 */
.action-button {
  color: var(--el-text-color-regular);
  border-radius: 50%;
  transition: all var(--layout-transition-duration);
  width: 32px;
  height: 32px;
}

/* 侧边栏模式下使用稍大的按钮 */
.sidebar-mode .action-button {
  width: 36px;
  height: 36px;
}

.action-button:hover {
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-light);
}

/* 标签页操作下拉菜单 */
.tabs-actions-dropdown {
  margin-right: 4px;
}

/* 滚动条样式 */
.tabs-left::-webkit-scrollbar {
  height: 4px;
}

.tabs-left::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

.tabs-left::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 2px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .tabs-container {
    padding: 0 8px;
  }

  .tabs-right {
    margin-left: 8px;
    gap: 4px;
  }

  .action-button {
    width: 28px;
    height: 28px;
  }

  .tab-item {
    min-width: 60px;
    max-width: 120px;
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .tabs-right {
    /* 在极小屏幕上隐藏部分按钮，只保留用户信息 */
  }

  .tabs-right .action-button {
    display: none;
  }

  .tab-item {
    min-width: 50px;
    max-width: 100px;
  }
}
</style>
