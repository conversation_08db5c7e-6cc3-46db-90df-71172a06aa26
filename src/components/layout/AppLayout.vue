<script setup>
// 1. 导入
import { onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store'
import { useLayoutStore } from '@/store/modules/layout'
import { LAYOUT_MODES } from '@/constants/layout'
import AppSidebar from './sidebar/AppSidebar.vue'
import AppHeader from './header/AppHeader.vue'
import AppMain from './AppMain.vue'
import TopNavLayout from './TopNavLayout.vue'
import LayoutSettings from './LayoutSettings.vue'

// 2. Props & Emits 定义
// (此组件无需props和emits)

// 3. 响应式状态
const router = useRouter()
const userStore = useUserStore()
const layoutStore = useLayoutStore()

// 4. 计算属性
const currentLayoutComponent = computed(() => {
  switch (layoutStore.currentLayoutMode) {
    case LAYOUT_MODES.TOP:
      return TopNavLayout
    default:
      return null // 使用默认侧边栏布局
  }
})

// 5. 方法
function handleMenuSelect(index) {

  // 确保路径格式正确
  let path = index
  if (!path.startsWith('/')) {
    path = `/${path}`
  }

  router.push(path)
}

// 6. 生命周期钩子
onMounted(async () => {
  // 如果已登录但没有用户信息，则获取用户信息
  if (userStore.isLoggedIn) {
    try {
      await userStore.getUserInfo()
    } catch (_err) {
      // 静默处理，不显示错误提示
    }
  }
})
</script>

<template>
  <div class="app-container" :class="`layout-${layoutStore.currentLayoutMode}`">
    <!-- 使用动态组件渲染不同布局模式 -->
    <component
      v-if="currentLayoutComponent"
      :is="currentLayoutComponent"
      @menu-select="handleMenuSelect"
    />

    <!-- 默认侧边栏布局 -->
    <template v-else>
      <!-- 侧边栏 -->
      <AppSidebar
        v-if="layoutStore.showSidebar"
        :is-collapsed="layoutStore.isCollapsed"
        @menu-select="handleMenuSelect"
        @toggle-sidebar="layoutStore.toggleSidebar"
      />

      <!-- 主内容区 -->
      <div class="main-container">
        <!-- 顶部导航栏 -->
        <AppHeader @toggle-sidebar="layoutStore.toggleSidebar" />

        <!-- 内容区域 -->
        <AppMain />
      </div>
    </template>

    <!-- 布局设置组件 -->
    <LayoutSettings />
  </div>
</template>

<style scoped>
/* 全局容器 */
.app-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  transition: all var(--layout-transition-duration);
}

/* 侧边栏布局模式 */
.layout-sidebar {
  flex-direction: row;
}

/* 顶部布局模式 */
.layout-top {
  flex-direction: column;
}

/* 主内容区 */
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--el-bg-color-page);
  transition: all var(--layout-transition-duration);
  margin-left: var(--layout-sidebar-margin, 0);
}
</style>
