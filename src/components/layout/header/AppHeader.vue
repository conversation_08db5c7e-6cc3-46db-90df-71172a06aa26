<script setup>
// 1. 导入
import { useDarkMode } from '@/composables/useDarkMode'
import { useLayoutStore } from '@/store/modules/layout'
import {
  <PERSON>,
  Sunny,
  Setting
} from '@element-plus/icons-vue'
import AppUserDropdown from './AppUserDropdown.vue'
import AppTabs from '../AppTabs.vue'

// 2. Emits定义
// const emit = defineEmits(['toggle-sidebar'])

// 3. 响应式状态
const { isDark, toggleDark } = useDarkMode()
const layoutStore = useLayoutStore()

// 4. 方法
function handleToggleSettings() {
  layoutStore.toggleSettings()
}
</script>

<template>
  <div class="header-container">
    <!-- navbar（仅在顶部模式下显示） -->
    <div
      v-if="layoutStore.currentLayoutMode === 'top'"
      class="navbar"
    >
      <div class="navbar-left">
        <!-- 顶部模式下的左侧内容 -->
      </div>

      <div class="navbar-right">
        <!-- 暗夜模式切换 -->
        <el-button
          circle
          class="dark-mode-toggle"
          @click="toggleDark"
        >
          <el-icon>
            <Moon v-if="!isDark" />
            <Sunny v-else />
          </el-icon>
        </el-button>

        <!-- 布局设置 -->
        <el-button
          v-if="layoutStore.headerConfig.showSettings"
          circle
          class="settings-toggle"
          @click="handleToggleSettings"
        >
          <el-icon>
            <Setting />
          </el-icon>
        </el-button>

        <!-- 用户信息 -->
        <AppUserDropdown />
      </div>
    </div>

    <!-- 标签页（始终显示） -->
    <AppTabs />
  </div>
</template>

<style scoped>
.header-container {
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color);
  box-shadow: var(--el-box-shadow-light);
  z-index: 100;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 48px; /* 固定高度 */
  transition: all var(--layout-transition-duration);
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark-mode-toggle,
.settings-toggle {
  color: var(--el-text-color-regular);
  border-radius: 50%;
  transition: all var(--layout-transition-duration);
  width: 32px;
  height: 32px;
}

.dark-mode-toggle:hover,
.settings-toggle:hover {
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-light);
}

.settings-toggle {
  animation: none;
}

.settings-toggle:hover {
  animation: rotate 0.5s ease-in-out;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(90deg);
  }
}
</style> 