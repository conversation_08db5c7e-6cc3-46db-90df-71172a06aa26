<script setup>
// 1. 导入
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store'
import { ArrowRight, User, Key, SwitchButton } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 2. 响应式状态
const router = useRouter()
const userStore = useUserStore()

// 3. 计算属性
const username = computed(() => userStore.displayName)

// 4. 方法
async function handleLogout() {
  try {
    const success = await userStore.logoutAction()
    if (success) {
      ElMessage.success('退出登录成功')
      router.push('/login')
    }
  } catch (error) {
    ElMessage.error('退出登录失败，请重试', error)
  }
}

function handleProfile() {
  router.push('/profile')
}

function handleChangePassword() {
  // TODO: 实现修改密码功能
  ElMessage.info('修改密码功能待实现')
}
</script>

<template>
  <el-dropdown trigger="click">
    <div class="avatar-wrapper">
      <el-avatar :size="28" class="user-avatar">
        {{ username.slice(0, 1) }}
      </el-avatar>
      <span class="user-name">{{ username }}</span>
      <el-icon class="dropdown-icon">
        <ArrowRight />
      </el-icon>
    </div>
    <template #dropdown>
      <el-dropdown-menu class="user-dropdown">
        <el-dropdown-item @click="handleProfile">
          <el-icon><User /></el-icon>
          <span>个人信息</span>
        </el-dropdown-item>
        <el-dropdown-item @click="handleChangePassword">
          <el-icon><Key /></el-icon>
          <span>修改密码</span>
        </el-dropdown-item>
        <el-dropdown-item divided @click="handleLogout">
          <el-icon><SwitchButton /></el-icon>
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<style scoped>
.avatar-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 16px;
  transition: all 0.3s;
}

.avatar-wrapper:hover {
  background-color: var(--el-fill-color-light);
}

.user-avatar {
  background-color: var(--el-color-primary);
  font-size: 12px;
  margin-right: 6px;
}

.user-name {
  font-size: 13px;
  color: var(--el-text-color-regular);
  margin-right: 4px;
}

.dropdown-icon {
  color: var(--el-text-color-regular);
  font-size: 10px;
  transition: transform 0.3s;
}

.avatar-wrapper:hover .dropdown-icon {
  transform: rotate(90deg);
}

.user-dropdown :deep(.el-dropdown-item) {
  display: flex;
  align-items: center;
}

.user-dropdown :deep(.el-dropdown-item .el-icon) {
  margin-right: 8px;
}
</style>
