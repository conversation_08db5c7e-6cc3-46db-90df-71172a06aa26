<script setup>
// 1. 导入
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { usePermissionStore } from '@/store/modules/permission'

// 2. 响应式状态
const route = useRoute()
const permissionStore = usePermissionStore()

// 3. 计算属性
const breadcrumbs = computed(() => {
  // 如果是登录页，不显示面包屑
  if (route.path === '/login') {
    return []
  }

  const pathArray = route.path.split('/').filter(Boolean)
  const breadcrumbItems = [{ name: '首页', path: '/dashboard' }]

  // 如果有子路径，遍历添加到面包屑
  if (pathArray.length > 0) {
    pathArray.forEach((path, index) => {
      // 排除dashboard，因为已经添加了首页
      if (path !== 'dashboard') {
        const currentPath = `/${pathArray.slice(0, index + 1).join('/')}`
        const title = getMenuTitle(currentPath)
        breadcrumbItems.push({
          name: title,
          path: currentPath
        })
      }
    })
  }

  return breadcrumbItems
})

const pageTitle = computed(() => {
  return breadcrumbs.value.length > 0
    ? (breadcrumbs.value[breadcrumbs.value.length - 1].name || '仪表盘')
    : '仪表盘'
})

// 4. 方法
function getMenuTitle(targetPath) {
  // 从动态菜单数据中查找匹配的菜单项
  const findMenuByPath = (menus, path) => {
    for (const menu of menus) {
      if (menu.path === path) {
        return menu
      }
      if (menu.children && menu.children.length > 0) {
        const found = findMenuByPath(menu.children, path)
        if (found) return found
      }
    }
    return null
  }

  const menuItem = findMenuByPath(permissionStore.menus, targetPath)
  return menuItem ? menuItem.name : ''
}
</script>

<template>
  <div class="breadcrumb-container">
    <div class="page-title">{{ pageTitle }}</div>
    
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item 
        v-for="(item, index) in breadcrumbs" 
        :key="index"
        :to="{ path: item.path }"
      >
        <span class="breadcrumb-item">{{ item.name }}</span>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<style scoped>
.breadcrumb-container {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-right: 16px;
}

.breadcrumb {
  display: flex;
  align-items: center;
}

.breadcrumb-item {
  color: var(--el-text-color-regular);
  transition: color 0.3s;
}

.breadcrumb-item:hover {
  color: var(--el-color-primary);
}


</style> 