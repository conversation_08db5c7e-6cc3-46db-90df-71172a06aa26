import { useUserStore } from '@/store/modules/user'

/**
 * 权限控制指令
 * 根据用户权限和角色控制DOM元素的显示/隐藏
 */

function checkPermission(el, binding) {
  const userStore = useUserStore()
  const { value, modifiers } = binding

  if (!value) {
    return
  }

  let hasPermission = false

  if (modifiers.all) {
    hasPermission = checkAllPermissions(userStore, value)
  } else {
    hasPermission = checkSinglePermission(userStore, value)
  }

  if (!hasPermission) {
    if (el.parentNode) {
      el.parentNode.removeChild(el)
    }
  }
}

/**
 * 检查单个权限或角色
 * @param {Object} userStore - 用户store实例
 * @param {string|Array} permission - 权限标识或角色
 * @returns {boolean} 是否有权限
 */
function checkSinglePermission(userStore, permission) {
  if (!permission) return true

  const { permissions, roles } = userStore

  // 如果是数组，检查是否有任一权限
  if (Array.isArray(permission)) {
    return permission.some(p =>
      permissions.includes(p) || roles.includes(p)
    )
  }

  // 单个权限检查
  return permissions.includes(permission) || roles.includes(permission)
}

/**
 * 检查所有权限
 * @param {Object} userStore - 用户store实例
 * @param {string|Array} permissions - 权限标识数组
 * @returns {boolean} 是否拥有所有权限
 */
function checkAllPermissions(userStore, permissions) {
  if (!permissions) return true

  const { permissions: userPermissions, roles: userRoles } = userStore

  // 确保是数组
  const permissionArray = Array.isArray(permissions) ? permissions : [permissions]

  // 检查是否拥有所有权限
  return permissionArray.every(p =>
    userPermissions.includes(p) || userRoles.includes(p)
  )
}

const permission = {
  mounted(el, binding) {
    checkPermission(el, binding)
  },

  updated(el, binding) {
    if (binding.value !== binding.oldValue) {
      checkPermission(el, binding)
    }
  }
}

export function hasPermission(permissions, requireAll = false) {
  const userStore = useUserStore()

  if (!permissions) return true

  if (requireAll) {
    return checkAllPermissions(userStore, permissions)
  } else {
    return checkSinglePermission(userStore, permissions)
  }
}

export function usePermission() {
  const userStore = useUserStore()

  return {
    hasPermission: (permissions) => checkSinglePermission(userStore, permissions),
    hasAllPermissions: (permissions) => checkAllPermissions(userStore, permissions),
    checkPermission: hasPermission,
    permissions: userStore.permissions,
    roles: userStore.roles
  }
}

export default permission
