import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

/**
 * 路由配置
 * 包含静态路由和动态路由管理
 */

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      name: 'layout',
      component: () => import('../components/layout/AppLayout.vue'),
      redirect: '/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('../views/dashboard/DashboardView.vue'),
          meta: {
            title: '仪表盘',
            requiresAuth: true
            // 暂时不设置权限要求，让所有登录用户都能访问
          }
        },
      ]
    },
    {
      path: '/403',
      name: 'forbidden',
      component: () => import('../views/error/403.vue'),
      meta: {
        title: '无权限访问',
        requiresAuth: false
      }
    },
    {
      path: '/404',
      name: 'notFound',
      component: () => import('../views/error/404.vue'),
      meta: {
        title: '页面不存在',
        requiresAuth: false
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'initialCatchAll',
      component: () => import('../views/error/PageNotFound.vue'),
      meta: {
        title: '页面加载中...',
        requiresAuth: false,
        isInitialCatchAll: true
      }
    }
  ],
})

const WHITE_LIST = ['/login', '/404', '/403']

router.beforeEach(async (to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - 管理系统` : '管理系统'

  if (WHITE_LIST.includes(to.path)) {
    if (to.path === '/login' && getToken()) {
      next({ path: '/' })
    } else {
      next()
    }
    return
  }

  // 检查是否已登录
  if (!getToken()) {
    ElMessage.warning('请先登录')
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 对于需要认证的路由，确保权限数据已加载
  const { usePermissionStore } = await import('@/store/modules/permission')
  const permissionStore = usePermissionStore()

  // 如果权限数据未加载，先加载权限数据
  if (!permissionStore.hasMenus) {
    try {
      const success = await permissionStore.fetchUserMenus()
      if (!success) {
        ElMessage.error('获取权限信息失败')
        next('/403')
        return
      }
      
      // 权限数据加载成功，动态路由已注册
      // 重新导航到目标路由
      next(to.fullPath)
      return
    } catch (error) {
      console.error('加载权限数据失败:', error)
      next('/403')
      return
    }
  }

  // 检查路由权限
  if (to.meta?.permission) {
    const hasRoutePermission = permissionStore.hasPermission(to.meta.permission)

    if (!hasRoutePermission) {
      ElMessage.warning('您没有访问该页面的权限')
      next('/403')
      return
    }
  }

  next()
})

router.afterEach((_to, _from) => {
  // 路由跳转完成
})

router.onError((error) => {
  console.error('路由组件加载错误:', error)

  if (error.message.includes('Failed to fetch dynamically imported module') ||
      error.message.includes('Loading chunk')) {
    router.push('/404')
  }
})

export default router
