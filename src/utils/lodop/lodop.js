// src/utils/lodop.js

//==本JS是加载Lodop插件或Web打印服务CLodop/Lodop7的综合示例，已修改为适合Vue等现代框架的模块==

//用双端口加载主JS文件Lodop.js(或CLodopfuncs.js兼容老版本)以防其中某端口被占:
const MainJS = "CLodopfuncs.js";
const URL_WS1 = "ws://localhost:8000/" + MainJS;
const URL_WS2 = "ws://localhost:18000/" + MainJS;
const URL_HTTP1 = "http://localhost:8000/" + MainJS;
const URL_HTTP2 = "http://localhost:18000/" + MainJS;
const URL_HTTP3 = "https://localhost.lodop.net:8443/" + MainJS;

let CreatedOKLodopObject, CLodopIsLocal, LoadJsState;

// 新增：一个标记来防止重复加载脚本
let isLodopScriptLoaded = false;

// 内部加载函数，基本保持不变，但不再导出
function loadLodopScript() {
    if (!needCLodop()) return;
    CLodopIsLocal = !!((URL_WS1 + URL_WS2).match(/\/\/localho|\/\/127.0.0./i));
    LoadJsState = "loadingA";
    if (!window.WebSocket && window.MozWebSocket) window.WebSocket = window.MozWebSocket;
    //ws方式速度快(小于200ms)且可避免CORS错误,但要求Lodop版本足够新:
    try {
        var WSK1 = new WebSocket(URL_WS1);
        WSK1.onopen = function (e) { setTimeout(checkOrTryHttp, 200); }
        WSK1.onmessage = function (e) { if (!window.getCLodop) eval(e.data); }
        WSK1.onerror = function (e) {
            var WSK2 = new WebSocket(URL_WS2);
            WSK2.onopen = function (e) { setTimeout(checkOrTryHttp, 200); }
            WSK2.onmessage = function (e) { if (!window.getCLodop) eval(e.data); }
            WSK2.onerror = function (e) { checkOrTryHttp(); }
        }
    } catch (e) {
        checkOrTryHttp();
    }
}

/**
 * 新增：导出的安装函数
 * 组件将调用此函数来确保Lodop脚本已加载。
 * 借助 isLodopScriptLoaded 标记，它只会实际执行一次。
 */
export function installLodop() {
    if (isLodopScriptLoaded) {
        return;
    }
    isLodopScriptLoaded = true;
    loadLodopScript();
}

//==判断是否需要CLodop(那些不支持插件的浏览器):==
function needCLodop() {
    try {
        var ua = navigator.userAgent;
        if (ua.match(/Windows\sPhone/i) ||
            ua.match(/iPhone|iPod|iPad/i) ||
            ua.match(/Android/i) ||
            ua.match(/Edge\D?\d+/i))
            return true;
        var verTrident = ua.match(/Trident\D?\d+/i);
        var verIE = ua.match(/MSIE\D?\d+/i);
        var verOPR = ua.match(/OPR\D?\d+/i);
        var verFF = ua.match(/Firefox\D?\d+/i);
        var x64 = ua.match(/x64/i);
        if ((!verTrident) && (!verIE) && (x64)) return true;
        else if (verFF) {
            verFF = verFF[0].match(/\d+/);
            if ((verFF[0] >= 41) || (x64)) return true;
        } else if (verOPR) {
            verOPR = verOPR[0].match(/\d+/);
            if (verOPR[0] >= 32) return true;
        } else if ((!verTrident) && (!verIE)) {
            var verChrome = ua.match(/Chrome\D?\d+/i);
            if (verChrome) {
                verChrome = verChrome[0].match(/\d+/);
                if (verChrome[0] >= 41) return true;
            }
        }
        return false;
    } catch (err) {
        return true;
    }
}

//==检查加载成功与否，如没成功则用http(s)再试==
function checkOrTryHttp() {
    if (window.getCLodop) {
        LoadJsState = "complete";
        return true;
    }
    if (LoadJsState == "loadingB" || LoadJsState == "complete") return;
    LoadJsState = "loadingB";
    var head = document.head || document.getElementsByTagName("head")[0] || document.documentElement;
    var JS1 = document.createElement("script");
    var JS2 = document.createElement("script");
    var JS3 = document.createElement("script");
    JS1.src = URL_HTTP1;
    JS2.src = URL_HTTP2;
    JS3.src = URL_HTTP3;
    JS1.onload = JS2.onload = JS3.onload = JS2.onerror = JS3.onerror = function () {
        LoadJsState = "complete";
    }
    JS1.onerror = function (e) {
        if (window.location.protocol !== 'https:')
            head.insertBefore(JS2, head.firstChild); else
            head.insertBefore(JS3, head.firstChild);
    }
    head.insertBefore(JS1, head.firstChild);
}

//==加载Lodop对象的主过程, 改为可控的导出函数:==
export function loadLodop() {
    if (!needCLodop()) return;
    CLodopIsLocal = !!((URL_WS1 + URL_WS2).match(/\/\/localho|\/\/127.0.0./i));
    LoadJsState = "loadingA";
    if (!window.WebSocket && window.MozWebSocket) window.WebSocket = window.MozWebSocket;
    //ws方式速度快(小于200ms)且可避免CORS错误,但要求Lodop版本足够新:
    try {
        var WSK1 = new WebSocket(URL_WS1);
        WSK1.onopen = function (e) { setTimeout(checkOrTryHttp, 200); }
        WSK1.onmessage = function (e) { if (!window.getCLodop) eval(e.data); }
        WSK1.onerror = function (e) {
            var WSK2 = new WebSocket(URL_WS2);
            WSK2.onopen = function (e) { setTimeout(checkOrTryHttp, 200); }
            WSK2.onmessage = function (e) { if (!window.getCLodop) eval(e.data); }
            WSK2.onerror = function (e) { checkOrTryHttp(); }
        }
    } catch (e) {
        checkOrTryHttp();
    }
}

//==获取LODOP对象主过程,判断是否安装、需否升级 (关键修改):==
//!! 修改: 不再直接操作DOM, 而是返回一个包含LODOP对象或错误信息的Object
export function getLodop(oOBJECT, oEMBED) {
    const strFontTag = "<br><font color='#FF00FF'>打印控件";
    const strLodopInstall = strFontTag + "未安装!点击这里<a href='install_lodop32.exe' target='_self'>执行安装</a>";
    const strLodopUpdate = strFontTag + "需要升级!点击这里<a href='install_lodop32.exe' target='_self'>执行升级</a>";
    const strLodop64Install = strFontTag + "未安装!点击这里<a href='install_lodop64.exe' target='_self'>执行安装</a>";
    const strLodop64Update = strFontTag + "需要升级!点击这里<a href='install_lodop64.exe' target='_self'>执行升级</a>";
    const strCLodopInstallA = "<br><font color='#FF00FF'>Web打印服务CLodop未安装启动，点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>下载执行安装</a>";
    const strCLodopInstallB = "<br>（若此前已安装过，可<a href='CLodop.protocol:setup' target='_self'>点这里直接再次启动</a>）";
    const strCLodopUpdate = "<br><font color='#FF00FF'>Web打印服务CLodop需升级!点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>执行升级</a>";
    const strInstallOK = "，成功后请刷新本页面或重启浏览器。</font>";

    let LODOP;
    try {
        const isWinIE = (/MSIE/i.test(navigator.userAgent)) || (/Trident/i.test(navigator.userAgent));
        const isWinIE64 = isWinIE && (/x64/i.test(navigator.userAgent));

        if (needCLodop()) {
            try {
                LODOP = window.getCLodop();
            } catch (err) { }

            if (!LODOP && LoadJsState !== "complete") {
                if (!LoadJsState)
                    // MODIFIED: Return error message instead of alert
                    return {
                        error: "未曾加载Lodop主JS文件，请先调用loadLodop过程."
                    };
                else
                    // MODIFIED: Return error message instead of alert
                    return {
                        error: "网页还没下载完毕，请稍等一下再操作."
                    };
            }

            let strAlertMessage;
            if (!LODOP) {
                strAlertMessage = strCLodopInstallA + (CLodopIsLocal ? strCLodopInstallB : "");
                // MODIFIED: Return error message
                return {
                    error: strAlertMessage + strInstallOK
                };
            } else {
                if (window.CLODOP.CVERSION < "6.5.9.6") { // Example version, adjust as needed
                    strAlertMessage = strCLodopUpdate;
                    // MODIFIED: Return error message
                    return {
                        error: strAlertMessage + strInstallOK
                    };
                }
            }
        } else {
            //==如果页面有Lodop插件就直接使用,否则新建:==
            if (oOBJECT || oEMBED) {
                if (isWinIE) LODOP = oOBJECT;
                else LODOP = oEMBED;
            } else if (!CreatedOKLodopObject) {
                LODOP = document.createElement("object");
                LODOP.setAttribute("width", 0);
                LODOP.setAttribute("height", 0);
                LODOP.setAttribute("style", "position:absolute;left:0px;top:-100px;width:0px;height:0px;");
                if (isWinIE)
                    LODOP.setAttribute("classid", "clsid:2105C259-1E0C-4534-8141-A753534CB4CA");
                else
                    LODOP.setAttribute("type", "application/x-print-lodop");
                document.documentElement.appendChild(LODOP);
                CreatedOKLodopObject = LODOP;
            } else
                LODOP = CreatedOKLodopObject;
            //==Lodop插件未安装时提示下载地址:==
            if ((!LODOP) || (!LODOP.VERSION)) {
                // MODIFIED: Return error message
                return {
                    error: (isWinIE64 ? strLodop64Install : strLodopInstall) + strInstallOK
                };
            }
            if (LODOP.VERSION < "6.2.2.6") {
                // MODIFIED: Return error message
                return {
                    error: (isWinIE64 ? strLodop64Update : strLodopUpdate) + strInstallOK
                };
            }
        }
        //===如下空白位置适合调用统一功能(如注册语句、语言选择等):=======================

        //===============================================================================
        // MODIFIED: Return LODOP object on success
        return {
            lodop: LODOP,
            error: null
        };
    } catch (err) {
        // MODIFIED: Return error message
        return {
            error: "getLodop出错:" + err
        };
    }
}