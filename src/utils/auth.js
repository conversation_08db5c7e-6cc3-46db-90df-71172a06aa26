// Token在本地存储中的键名
const TOKEN_KEY = 'admin_token'
const USER_INFO_KEY = 'admin_user_info'

/**
 * 获取Token
 * @returns {string|null} 存储的Token或null
 */
export function getToken() {
    return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置Token
 * @param {string} token - 需要存储的Token
 */
export function setToken(token) {
    localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除Token
 */
export function removeToken() {
    localStorage.removeItem(TOKEN_KEY)
}

/**
 * 保存用户信息
 * @param {Object} userInfo - 用户信息对象
 */
export function setUserInfo(userInfo) {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息对象或null
 */
export function getUserInfo() {
    const userInfo = localStorage.getItem(USER_INFO_KEY)
    return userInfo ? JSON.parse(userInfo) : null
}

/**
 * 移除用户信息
 */
export function removeUserInfo() {
    localStorage.removeItem(USER_INFO_KEY)
}

/**
 * 清除所有认证相关数据
 */
export function clearAuth() {
    removeToken()
    removeUserInfo()
} 