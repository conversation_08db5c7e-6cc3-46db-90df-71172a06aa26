import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { getToken, clearAuth } from './auth'

// API路径配置
const API_PREFIX = '/api'
const baseApiUrl = import.meta.env.VITE_API_BASE_URL || ''

// 防止重复跳转登录页的标志位
let isRedirectingToLogin = false

// 请求重试配置
const RETRY_CONFIG = {
    maxRetries: 3,
    retryDelay: 1000, // 基础延迟时间(ms)
    retryCondition: (error) => {
        // 只对网络错误和5xx服务器错误进行重试
        return !error.response || (error.response.status >= 500 && error.response.status < 600)
    }
}

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 处理登录跳转
const handleLoginRedirect = async () => {
    if (isRedirectingToLogin) return

    isRedirectingToLogin = true

    // 清除认证信息
    clearAuth()

    // 清除标签页数据
    try {
        const { useTabsStore } = await import('@/store/modules/tabs')
        const tabsStore = useTabsStore()
        tabsStore.clearAllTabs()
    } catch (error) {
        console.warn('清除标签页数据失败:', error)
    }

    router.push('/login').finally(() => {
        // 延迟重置标志位，避免快速重复跳转
        setTimeout(() => {
            isRedirectingToLogin = false
        }, 1000)
    })
}

// 创建axios实例
const service = axios.create({
    baseURL: baseApiUrl.endsWith(API_PREFIX) ? baseApiUrl : `${baseApiUrl}${API_PREFIX}`, // 确保基础URL以/api结尾
    timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        // 如果存在token，添加到请求头
        const token = getToken()
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`
        }
        return config
    },
    error => {
        ElMessage.error('请求发送失败')
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        // 检查是否为二进制数据响应（如PDF文件）
        if (response.config.responseType === 'blob') {
            // 对于blob响应，直接返回response对象，让调用方处理
            return response
        }

        const res = response.data

        // 根据后端接口规范调整，这里假设成功状态码为0或200
        if (res.code === 0 || res.code === 200) {
            return res
        } else if (res.msg || res.message) {
            const aMsg = res.msg ? res.msg : res.message
            ElMessage({
                message: aMsg,
                type: 'error',
                duration: 3 * 1000
            })
        } else {
            // 处理业务错误
            ElMessage({
                message: res.message || '请求失败',
                type: 'error',
                duration: 5 * 1000
            })

            // 处理特定错误码，例如token过期
            if (res.code === 401) {
                // token过期或无效，统一处理登录跳转
                handleLoginRedirect()
            }

            return Promise.reject(new Error(res.message || '未知错误'))
        }
    },
    async error => {
        const config = error.config

        // 处理401认证错误
        if (error.response?.status === 401) {
            handleLoginRedirect()
            return Promise.reject(error)
        }

        // 请求重试逻辑
        if (RETRY_CONFIG.retryCondition(error) && (!config._retryCount || config._retryCount < RETRY_CONFIG.maxRetries)) {
            config._retryCount = config._retryCount || 0
            config._retryCount += 1

            // 指数退避延迟
            const delayTime = RETRY_CONFIG.retryDelay * Math.pow(2, config._retryCount - 1)
            await delay(delayTime)

            return service(config)
        }

        // 处理HTTP错误
        const message = error.response?.data?.message || error.message || '网络错误'
        ElMessage({
            message,
            type: 'error',
            duration: 5 * 1000
        })

        return Promise.reject(error)
    }
)

export default service 