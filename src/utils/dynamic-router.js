import router from '@/router'

/**
 * 动态路由管理器
 * 负责将菜单数据转换为Vue Router路由配置，并进行动态注册和清理
 */

const MENU_TYPE = {
  MENU: 0,
  BUTTON: 1
}

const MENU_STATUS = {
  DISABLED: 0,
  ENABLED: 1
}

let dynamicRouteNames = []

function transformMenusToRoutes(menuData, parentName = '', parentPath = '') {
  if (!Array.isArray(menuData)) {
    return []
  }

  const routes = []

  menuData.forEach(menu => {
    if (menu.type === MENU_TYPE.MENU && menu.status === MENU_STATUS.ENABLED) {
      const route = createRouteFromMenu(menu, parentName, parentPath)
      if (route) {
        routes.push(route)
      }
    }
  })

  return routes
}

function createRouteFromMenu(menu, parentName = '', parentPath = '') {
  try {
    const routeName = generateRouteName(menu, parentName)
    const routePath = processRoutePath(menu.path, parentPath)
    const route = createBaseRouteConfig(menu, routeName, routePath)

    configureRouteComponent(route, menu)
    configureChildRoutes(route, menu, routeName)

    return route
  } catch (error) {
    console.error('创建路由配置失败:', error, menu)
    return null
  }
}

function generateRouteName(menu, parentName = '') {
  let routeName = `menu_${menu.id}`
  if (parentName) {
    routeName = `${parentName}_${routeName}`
  }
  return routeName
}

function processRoutePath(menuPath, parentPath = '') {
  let routePath = menuPath

  if (parentPath && routePath.startsWith(parentPath + '/')) {
    routePath = routePath.substring(parentPath.length + 1)
  } else if (routePath.startsWith('/')) {
    routePath = routePath.substring(1)
  }

  return routePath
}

function createBaseRouteConfig(menu, routeName, routePath) {
  return {
    path: routePath,
    name: routeName,
    meta: {
      title: menu.name,
      icon: menu.icon,
      permission: menu.permission,
      hidden: menu.hidden === 1,
      requiresAuth: true,
      menuId: menu.id
    }
  }
}

function configureRouteComponent(route, menu) {
  if (menu.component && menu.component.trim() !== '') {
    route.component = createAsyncComponent(menu.component)
  } else if (!(menu.children && menu.children.length > 0)) {
    route.component = () => import('../views/error/PageNotFound.vue')
  }
}

function configureChildRoutes(route, menu, routeName) {
  if (menu.children && menu.children.length > 0) {
    const childRoutes = transformMenusToRoutes(menu.children, routeName, menu.path)
    if (childRoutes.length > 0) {
      route.children = childRoutes
    }
  }
}

function createAsyncComponent(componentPath) {
  let normalizedPath = componentPath
  if (normalizedPath.startsWith('/')) {
    normalizedPath = normalizedPath.substring(1)
  }

  if (!normalizedPath.endsWith('.vue')) {
    normalizedPath = `${normalizedPath}.vue`
  }

  return async () => {
    try {
      const module = await import(`../views/${normalizedPath}`)

      if (!module.default) {
        throw new Error(`Component has no default export: ${normalizedPath}`)
      }

      return module
    } catch (error) {
      console.error(`组件加载失败: ../views/${normalizedPath}`, error)
      return import('../views/error/PageNotFound.vue')
    }
  }
}

export function addDynamicRoutes(menuData) {
  try {
    removeDynamicRoutes()

    const routes = transformMenusToRoutes(menuData)

    if (routes.length === 0) {
      return false
    }

    const layoutRoute = router.getRoutes().find(route => route.name === 'layout')

    if (!layoutRoute) {
      console.error('未找到主布局路由')
      return false
    }

    routes.forEach(route => {
      try {
        router.addRoute('layout', route)
        dynamicRouteNames.push(route.name)

        if (route.children) {
          collectChildRouteNames(route.children, dynamicRouteNames)
        }
      } catch (routeError) {
        console.error(`添加路由失败: ${route.name}`, routeError)
      }
    })

    return true
  } catch (error) {
    console.error('添加动态路由失败:', error)
    return false
  }
}

function collectChildRouteNames(children, nameList) {
  children.forEach(child => {
    if (child.name) {
      nameList.push(child.name)
    }
    if (child.children) {
      collectChildRouteNames(child.children, nameList)
    }
  })
}

export function removeDynamicRoutes() {
  try {
    dynamicRouteNames.forEach(routeName => {
      if (router.hasRoute(routeName)) {
        router.removeRoute(routeName)
      }
    })

    dynamicRouteNames = []
  } catch (error) {
    console.error('清理动态路由失败:', error)
  }
}

export function getDynamicRouteNames() {
  return [...dynamicRouteNames]
}
