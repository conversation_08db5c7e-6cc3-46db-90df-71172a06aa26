/* 全局CSS变量定义 - 与Element UI协调 */
:root {
  /* 自定义主色调（避免与Element UI冲突） */
  --custom-primary-color: #409eff;
  --custom-primary-color-light: #79bbff;
  --custom-primary-color-lighter: #a6d0ff;
  --custom-primary-color-dark: #337ecc;

  /* 自定义辅助颜色 */
  --custom-success-color: #67c23a;
  --custom-warning-color: #e6a23c;
  --custom-danger-color: #f56c6c;
  --custom-info-color: #909399;

  /* 自定义间距 */
  --custom-spacing-xs: 4px;
  --custom-spacing-sm: 8px;
  --custom-spacing-md: 16px;
  --custom-spacing-lg: 24px;
  --custom-spacing-xl: 32px;

  /* 自定义圆角 */
  --custom-border-radius-sm: 2px;
  --custom-border-radius-md: 4px;
  --custom-border-radius-lg: 6px;
  --custom-border-radius-xl: 8px;

  /* 自定义阴影 */
  --custom-box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --custom-box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  /* 自定义字体大小 */
  --custom-font-size-xs: 12px;
  --custom-font-size-sm: 14px;
  --custom-font-size-md: 16px;
  --custom-font-size-lg: 18px;
  --custom-font-size-xl: 20px;

  /* 布局相关变量（保持原有） */
  --layout-sidebar-width: 200px;
  --layout-sidebar-collapsed-width: 60px;
  --layout-header-height: 48px;
  --layout-border-radius: 4px;
  --layout-content-padding: 16px;
  --layout-transition-duration: 0.3s;
  --layout-sidebar-margin: 0;

  /* 卡片相关变量 */
  --card-radius: 8px;
  --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 移除自定义深色主题变量，完全使用Element UI的深色主题 */