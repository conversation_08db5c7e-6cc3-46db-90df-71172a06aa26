/* 样式入口文件 */
@import './variables.css';
@import './element-plus.css';

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局基础样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
  transition: color 0.3s, background-color 0.3s;
}

#app {
  height: 100%;
  width: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: var(--el-border-radius-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
} 