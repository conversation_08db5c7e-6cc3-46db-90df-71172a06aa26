import { markRaw } from 'vue'
import {
  Monitor,
  Grid
} from '@element-plus/icons-vue'

// 布局模式常量
export const LAYOUT_MODES = {
  SIDEBAR: 'sidebar',
  TOP: 'top'
}

// 布局模式选项
export const LAYOUT_MODE_OPTIONS = [
  {
    label: '侧边栏模式',
    value: LAYOUT_MODES.SIDEBAR,
    icon: markRaw(Grid),
    description: '经典左侧导航布局'
  },
  {
    label: '顶部模式',
    value: LAYOUT_MODES.TOP,
    icon: markRaw(Monitor),
    description: '顶部水平导航布局'
  }
]

// 侧边栏主题选项
export const SIDEBAR_THEME_OPTIONS = [
  { label: '浅色', value: 'light' },
  { label: '深色', value: 'dark' }
]

// 主题颜色预设（简化为5种基本颜色）
export const THEME_COLORS = [
  { name: '默认蓝', color: '#409EFF' },
  { name: '翠绿色', color: '#67C23A' },
  { name: '警告橙', color: '#E6A23C' },
  { name: '危险红', color: '#F56C6C' },
  { name: '优雅紫', color: '#722ED1' }
]



// 默认布局配置
export const DEFAULT_LAYOUT_CONFIG = {
  // 布局模式
  layoutMode: LAYOUT_MODES.SIDEBAR,

  // 侧边栏配置（简化，使用固定值）
  sidebarConfig: {
    theme: 'light'
  },

  // 头部配置（简化）
  headerConfig: {
    showSettings: true
  },

  // 主题配置
  themeConfig: {
    primaryColor: '#409EFF'
  }
}

