import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import './styles/index.css'
import { useDarkMode } from './composables/useDarkMode'

import App from './App.vue'
import router from './router'
import permission from './directives/permission'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 注册权限指令
app.directive('permission', permission)

// 配置Element Plus全局属性
app.provide('$ELEMENT', {
  size: 'default'
})

// 初始化暗夜模式
const { initDarkMode } = useDarkMode()
initDarkMode()

app.mount('#app')
