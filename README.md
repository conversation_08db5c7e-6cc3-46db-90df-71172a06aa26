## Project Setup

```sh
yarn
```

### Compile and Hot-Reload for Development

```sh
yarn dev
```

### Compile and Minify for Production

```sh
yarn build
```

## Project 


```
├── node_modules/         # 存放项目依赖的 npm 包
├── public/               # 静态资源目录，不会被 Webpack/Vite 处理，直接复制到构建输出目录
│   └── favicon.ico       # 网站图标
├── src/                  # 源代码核心目录
│   ├── api/           	  # API 请求模块 (例如: user.js, product.js)
│   ├── assets/           # 项目中使用的静态资源 (图片、字体等，会被 Vite 处理)
│   ├── components/       # 全局通用组件
│   │   ├── common/       # 基础通用组件 (如: SvgIcon, BaseCard)
│   │   └── layout/       # 布局组件 (如: Header, Sidebar, Footer)
│   ├── composables/      # Vue 3 组合式函数
│   ├── router/           # 路由配置 (index.js)
│   │   └── index.js      # 路由定义文件
│   ├── store/            # 状态管理 (Pinia)
│   │   ├── index.js      # Store 统一入口
│   │   └── modules/      # Pinia 模块 (如: user.js, counter.js)
│   ├── styles/           # 全局样式
│   │   ├── index.css     # 全局样式入口
│   │   ├── variables.css # 全局 CSS 变量
│   │   └── element-plus.css # Element Plus 样式定制
│   ├── utils/            # 工具函数 (如: request.js, auth.js)
│   ├── views/            # 页面级组件 (一个路由对应一个文件)
│   ├── App.vue           # 根组件，整个应用的顶层组件
│   └── main.js           # 应用入口
│
├── .editorconfig         # 编辑器配置
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
├── .gitignore            # Git 忽略文件列表
├── .prettierrc.json      # Prettier 配置文件
├── auto-imports.d.ts     # 自动导入类型定义文件 (自动生成)
├── components.d.ts       # 组件类型定义文件 (自动生成)
├── eslint.config.js      # ESLint 配置文件
├── package.json          # 项目配置文件，包含依赖列表和脚本命令
├── postcss.config.js     # PostCSS 配置
├── tailwind.config.js    # Tailwind CSS 配置
└── vite.config.js        # Vite 构建工具的配置文件
```
